type ProjectDocument {
  id: ID!
  name: String!
  projectId: String!
  createdAt: String!
  updatedAt: String
  status: DocumentStatus
  type: DocumentType
  category: DocumentCategory
  fileUrl: String
  workspaceGroup: WorkspaceGroup
}

type WorkspaceGroup {
  id: ID!
  name: String!
}

enum DocumentStatus {
  APPROVED
  PENDING
  DRAFT
  FINAL
  REJECTED
}

enum DocumentType {
  PDF
  IMAGE
  DRAWING
  REPORT
  CONTRACT
  SPECIFICATION
  SUBMITTAL
}

enum DocumentCategory {
  ProjectDocument
  WorkProgramme
  Correspondence
  AllForm
  StandardForm
  Photo
  TwoDDrawings
  BIMDrawings
}

input ProjectDocumentFilter {
  projectId: StringFilter
  name: StringFilter
  createdAt: DateFilter
  updatedAt: DateFilter
  status: DocumentStatusFilter
  type: DocumentTypeFilter
  category: DocumentCategoryFilter
  workspaceGroup: WorkspaceGroupFilter
}

input StringFilter {
  eq: String
  ne: String
  like: String
  ilike: String
  in: [String!]
  not_in: [String!]
}

input DateFilter {
  eq: String
  ne: String
  lt: String
  lte: String
  gt: String
  gte: String
  between: DateRange
}

input DateRange {
  lower: String!
  upper: String!
}

input DocumentStatusFilter {
  eq: DocumentStatus
  ne: DocumentStatus
  in: [DocumentStatus!]
}

input DocumentTypeFilter {
  eq: DocumentType
  ne: DocumentType
  in: [DocumentType!]
}

input DocumentCategoryFilter {
  eq: DocumentCategory
  ne: DocumentCategory
  in: [DocumentCategory!]
}

input WorkspaceGroupFilter {
  name: StringFilter
}

type Query {
  projectDocuments(
    filter: ProjectDocumentFilter
    limit: Int = 10
    offset: Int = 0
  ): ProjectDocumentConnection!
}

type ProjectDocumentConnection {
  nodes: [ProjectDocument!]!
  totalCount: Int!
}
