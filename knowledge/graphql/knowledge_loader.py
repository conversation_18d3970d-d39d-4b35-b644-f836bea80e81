import os
from typing import List

from agno.agent import AgentKnowledge
from agno.embedder.openai import OpenAIEmbedder
from agno.knowledge.text import TextKnowledgeBase
from agno.vectordb.pgvector import PgVector, SearchType

from db.session import db_url


def load_graphql_knowledge_texts() -> List[str]:
    """Load GraphQL knowledge from schema and examples files."""
    
    knowledge_dir = os.path.dirname(__file__)
    knowledge_texts = []
    
    # Load GraphQL schema and examples
    knowledge_files = ['schema.graphql', 'examples.md']
    
    for filename in knowledge_files:
        file_path = os.path.join(knowledge_dir, filename)
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                knowledge_texts.append(content)
            print(f"Loaded GraphQL knowledge from {filename}")
        except FileNotFoundError:
            print(f"Warning: Knowledge file {filename} not found")
        except Exception as e:
            print(f"Error loading {filename}: {str(e)}")
    
    return knowledge_texts


def get_graphql_knowledge() -> AgentKnowledge:
    """Create knowledge base for GraphQL agent with schema information."""
    
    knowledge_texts = load_graphql_knowledge_texts()
    
    return TextKnowledgeBase(
        texts=knowledge_texts,
        vector_db=PgVector(
            db_url=db_url,
            table_name="graphql_agent_knowledge",
            search_type=SearchType.hybrid,
            embedder=OpenAIEmbedder(id="text-embedding-3-small"),
        ),
    )