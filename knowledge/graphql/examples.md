# GraphQL Query Examples

Based on your ProjectDocument schema, here are practical query examples.

## ⚠️ Important: Always Include projectId

**CRITICAL**: All queries should always include `projectId` filter to scope results to a specific project. Never query without projectId as this could return documents from all projects.

## 🔑 Authentication & Headers

**REQUIRED HTTP Headers** for all GraphQL requests:

```json
{
  "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6NDMsInR5cGUiOiJVc2VyIiwiaWF0IjoxNzU1MjQ5ODYyLCJleHAiOjE3NTU4NTQ2NjJ9.4mT3fOOuDAQnl7iOm3kNcF-AzRtOfM2NnwJLQ7oeXGg",
  "project-id": 51
}
```

**Where to get these values:**
- `Authorization`: Available as `auth_token` parameter from chat API (`/api/routes/chat.py`)
- `project-id`: Available as `project_id` parameter from chat API (`/api/routes/chat.py`)

**Usage in GraphQL tool:**
```python
# Use these values when calling execute_graphql
execute_graphql(
    query="your_query_here",
    variables={"projectId": "51", ...},
    auth_token="Bearer_token_from_chat_api",  # Becomes Authorization header
    project_id="51"  # Becomes project-id header
)
```

## Site Diaries Queries

### Get Site Diaries for Project with Date Range
```graphql
query GetSiteDiaries(
  $paging: OffsetPaging, 
  $filter: ProjectDocumentFilter, 
  $sorting: [ProjectDocumentSort!]
) {
  projectDocuments(
    paging: $paging, 
    filter: $filter, 
    sorting: $sorting
  ) {
    pageInfo {
      hasNextPage
      hasPreviousPage
    }
    nodes {
      id
      name
      fileUrl
      category
      addedBy
      submittedAt
      workspaceGroup {
        name
      }
    }
    totalCount
  }
}
```

**Variables:**
```json
{
  "paging": {
    "limit": 99999,
    "offset": 0
  },
  "filter": {
    "projectId": { "eq": "51" },
    "workspaceGroup": { "name": { "like": "site diary" } },
    "submittedAt": {
      "between": {
        "lower": "2025-08-01T00:00:00Z",
        "upper": "2025-08-28T23:59:59Z"
      }
    }
  },
  "sorting": [
    {
      "field": "createdAt",
      "direction": "DESC"
    }
  ]
}
```

### Get Site Diaries for Current Month
```graphql
query GetCurrentMonthSiteDiaries(
  $projectId: String!,
  $startDate: String!,
  $endDate: String!
) {
  projectDocuments(
    paging: { limit: 1000, offset: 0 },
    filter: {
      projectId: { eq: $projectId },
      workspaceGroup: { name: { like: "site diary" } },
      submittedAt: {
        between: {
          lower: $startDate,
          upper: $endDate
        }
      }
    },
    sorting: [
      { field: "submittedAt", direction: "DESC" }
    ]
  ) {
    pageInfo {
      hasNextPage
      hasPreviousPage
    }
    nodes {
      id
      name
      fileUrl
      category
      addedBy
      submittedAt
      createdAt
      workspaceGroup {
        name
      }
    }
    totalCount
  }
}
```

**Variables:**
```json
{
  "projectId": "51",
  "startDate": "2025-08-01T00:00:00Z",
  "endDate": "2025-08-31T23:59:59Z"
}
```

### Get Recent Site Diaries (Last 7 Days)
```graphql
query GetRecentSiteDiaries($projectId: String!, $since: String!) {
  projectDocuments(
    paging: { limit: 100, offset: 0 },
    filter: {
      projectId: { eq: $projectId },
      workspaceGroup: { name: { like: "site diary" } },
      submittedAt: { gte: $since }
    },
    sorting: [
      { field: "submittedAt", direction: "DESC" }
    ]
  ) {
    nodes {
      id
      name
      fileUrl
      addedBy
      submittedAt
      workspaceGroup {
        name
      }
    }
    totalCount
  }
}
```

**Variables:**
```json
{
  "projectId": "51",
  "since": "2025-08-15T00:00:00Z"
}
```

## Available Enum Values

### DocumentStatus
- `APPROVED`
- `PENDING`
- `DRAFT`
- `FINAL`
- `REJECTED`

### DocumentType
- `PDF`
- `IMAGE`
- `DRAWING`
- `REPORT`
- `CONTRACT`
- `SPECIFICATION`
- `SUBMITTAL`

### DocumentCategory
- `ProjectDocument`
- `WorkProgramme`
- `Correspondence`
- `AllForm`
- `StandardForm`
- `Photo`
- `TwoDDrawings`
- `BIMDrawings`

## Common Filter Patterns

### String Filters
- `eq`: Exact match
- `ne`: Not equal
- `like`: SQL LIKE pattern (case-sensitive)
- `ilike`: SQL LIKE pattern (case-insensitive)
- `in`: Match any in list
- `not_in`: Not in list

### Date Filters
- `eq`: Exact date
- `ne`: Not equal
- `lt`: Less than
- `lte`: Less than or equal
- `gt`: Greater than
- `gte`: Greater than or equal
- `between`: Date range

## Best Practices

1. **Always use variables** for dynamic 
2. **Request only needed fields** to optimize performance
3. **Use pagination** with `limit` and `offset`
4. **Include `totalCount`** to understand result size
5. **Use `ilike` for case-insensitive** text searches
6. **Combine filters** for specific queries