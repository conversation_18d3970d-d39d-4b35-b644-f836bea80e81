"""
Analysis Knowledge Loader for Construction Document Analysis
"""
from pathlib import Path
from agno.agent import AgentKnowledge


def get_analysis_knowledge() -> AgentKnowledge:
    """
    Load analysis knowledge from markdown files.
    
    Returns:
        AgentKnowledge: Configured knowledge base for analysis
    """
    # Get the directory where this file is located
    knowledge_dir = Path(__file__).parent
    
    return AgentKnowledge(
        knowledge_base=[
            str(knowledge_dir / "construction_analysis_guide.md"),
        ],
        vector_db_config={
            "provider": "pgvector",
            "url": "postgresql://localhost:5432/agno_db",
            "schema": "agno",
        }
    )