from fastapi import FastAPI
from starlette.middleware.cors import CORSMiddleware

from api.routes.v1_router import v1_router
from api.settings import api_settings
from utils.logging_config import setup_logging
from utils.logfire_config import initialize_logfire, instrument_fastapi, shutdown_logfire
from config.settings import get_settings
from api.startup import startup_event, shutdown_event
import logging


def create_app() -> FastAPI:
    """Create a FastAPI App"""
    
    # Setup logging first
    setup_logging()
    logger = logging.getLogger(__name__)
    
    # Initialize Logfire monitoring
    logfire_enabled = initialize_logfire()
    if logfire_enabled:
        logger.info("🔥 Logfire monitoring initialized")
    
    settings = get_settings()

    # Create FastAPI App
    app: FastAPI = FastAPI(
        title=api_settings.title,
        version=api_settings.version,
        docs_url="/docs" if api_settings.docs_enabled else None,
        redoc_url="/redoc" if api_settings.docs_enabled else None,
        openapi_url="/openapi.json" if api_settings.docs_enabled else None,
        debug=settings.debug,
    )

    # Add Logfire instrumentation to FastAPI
    if logfire_enabled:
        instrument_fastapi(app)
        logger.info("🎯 FastAPI instrumented with Logfire")

    # Add v1 router
    app.include_router(v1_router)

    # Add Middlewares
    app.add_middleware(
        CORSMiddleware,
        allow_origins=api_settings.cors_origin_list,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Enhanced startup event with Logfire
    async def enhanced_startup():
        await startup_event()
        if logfire_enabled:
            logger.info("🚀 Application startup completed with Logfire monitoring")
    
    # Enhanced shutdown event with Logfire cleanup
    async def enhanced_shutdown():
        await shutdown_event()
        if logfire_enabled:
            shutdown_logfire()
            logger.info("🔧 Application shutdown completed, Logfire cleaned up")
    
    # Add startup and shutdown events
    app.add_event_handler("startup", enhanced_startup)
    app.add_event_handler("shutdown", enhanced_shutdown)

    return app


# Create a FastAPI app
app = create_app()
