"""
FastAPI routes for document vectorization.
"""
from fastapi import APIRouter, HTTPException, status, Depends
from tools.vector_db.models.vectorization import VectorizeDocumentRequest, VectorizeDocumentResponse
from tools.vector_db.services.document_service import DocumentService, DocumentProcessingError
from utils.error_handler import error_context
from utils.logging_config import log_tool_execution
import logging
import time

logger = logging.getLogger(__name__)

# Create router for vectorization endpoints
vectorization_router = APIRouter(prefix="/vectorize", tags=["Vectorization"])


def get_document_service() -> DocumentService:
    """Dependency to get document service instance."""
    return DocumentService()


@vectorization_router.post(
    "/document",
    response_model=VectorizeDocumentResponse,
    status_code=status.HTTP_200_OK,
    summary="Vectorize Document",
    description="Process and vectorize a document from URL for semantic search"
)
async def vectorize_document(
    request: VectorizeDocumentRequest,
    document_service: DocumentService = Depends(get_document_service)
) -> VectorizeDocumentResponse:
    """
    Vectorize a document for semantic search.
    
    This endpoint:
    1. Uses the provided document ID (no ID generation)
    2. Downloads the document from the provided URL
    3. Extracts text content (supports PDF files)
    4. Splits content into semantic chunks
    5. Generates embeddings using all-MiniLM-L6-v2
    6. Stores vectors in Milvus with project_id for organization
    
    Args:
        request: Vectorization request with id, project_id, and document details
        document_service: Injected document service
        
    Returns:
        VectorizeDocumentResponse with processing results
        
    Raises:
        HTTPException: If processing fails
    """
    start_time = time.time()
    logger.info(f"Vectorization request received for: {request.id} - {request.file_name}")
    logger.debug(f"Request details: ID={request.id}, Project={request.project_id}, URL={request.url}, Category={request.category}")
    
    try:
        with error_context("document_vectorization", document_id=request.id, project_id=request.project_id):
            # Check if document already exists (optional optimization)
            try:
                exists = await document_service.check_document_exists(request.id)
                if exists:
                    logger.warning(f"Document may already exist: {request.id} ({request.file_name})")
                    # You can choose to skip processing or continue anyway
            except Exception as e:
                logger.warning(f"Could not check document existence: {str(e)}")
            
            # Process the document
            result = await document_service.process_document(request)
            
            if result.success:
                duration = time.time() - start_time
                log_tool_execution(
                    logger, "document_vectorization", duration, True,
                    document_id=request.id, chunks_processed=result.chunks_processed
                )
                logger.info(
                    f"Document vectorized successfully: {result.document_id} "
                    f"({result.chunks_processed} chunks in {result.processing_time_seconds:.2f}s)"
                )
                return result
            else:
                duration = time.time() - start_time
                log_tool_execution(
                    logger, "document_vectorization", duration, False,
                    document_id=request.id, error=result.error
                )
                logger.error(f"Document processing failed: {result.error}")
                raise HTTPException(
                    status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                    detail={
                        "message": result.message,
                        "error": result.error,
                        "document_id": result.document_id,
                        "processing_time": result.processing_time_seconds
                    }
                )
            
    except DocumentProcessingError as e:
        logger.error(f"Document processing error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail={
                "message": "Document processing failed",
                "error": str(e),
                "type": "document_processing_error"
            }
        )
        
    except Exception as e:
        logger.error(f"Unexpected error during vectorization: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "message": "Internal server error during document processing",
                "error": str(e),
                "type": "internal_error"
            }
        )


@vectorization_router.get(
    "/stats",
    summary="Get Vectorization Statistics",
    description="Get current vectorization service statistics and configuration"
)
async def get_vectorization_stats(
    document_service: DocumentService = Depends(get_document_service)
) -> dict:
    """
    Get vectorization service statistics and configuration.
    
    Args:
        document_service: Injected document service
        
    Returns:
        Dictionary with service stats and configuration
    """
    try:
        stats = document_service.get_processing_stats()
        
        # Add vector database stats
        vector_stats = document_service.vector_service.get_collection_stats()
        stats.update({
            "vector_database": vector_stats,
            "status": "operational"
        })
        
        return stats
        
    except Exception as e:
        logger.error(f"Error getting vectorization stats: {str(e)}")
        return {
            "status": "error",
            "error": str(e),
            "message": "Failed to retrieve vectorization statistics"
        }


@vectorization_router.get(
    "/health",
    summary="Health Check",
    description="Check if vectorization service is healthy"
)
async def health_check() -> dict:
    """
    Health check for vectorization service.
    
    Returns:
        Health status information
    """
    try:
        # Basic health checks
        service = DocumentService()
        
        health_status = {
            "status": "healthy",
            "timestamp": service.vector_service.vector_db.get_collection_stats().get("timestamp", "unknown"),
            "services": {
                "document_parser": "operational",
                "embedding_service": "operational",
                "vector_database": "operational"
            }
        }
        
        return health_status
        
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail={
                "status": "unhealthy",
                "error": str(e),
                "message": "Vectorization service is not available"
            }
        )