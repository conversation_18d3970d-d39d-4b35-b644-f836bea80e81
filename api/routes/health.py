"""
Health check endpoints for monitoring system status.
"""
import asyncio
import time
from typing import Dict, Any
from fastapi import APIRouter, HTTPException, status
from pydantic import BaseModel

router = APIRouter(prefix="/health", tags=["Health"])


class ServiceHealth(BaseModel):
    """Individual service health."""
    status: str
    response_time_ms: float
    details: Dict[str, Any] = {}
    error: str = None


class HealthStatus(BaseModel):
    """Health status response model."""
    status: str
    timestamp: str
    version: str
    environment: str
    services: Dict[str, ServiceHealth]


async def check_database() -> ServiceHealth:
    """Check PostgreSQL database health."""
    start_time = time.time()
    
    try:
        import psycopg2
        conn = psycopg2.connect(
            host="pgvector",  # Docker service name
            port=5432,
            database="ai",
            user="ai", 
            password="ai"
        )
        cursor = conn.cursor()
        cursor.execute("SELECT 1")
        cursor.fetchone()
        cursor.close()
        conn.close()
        
        response_time = (time.time() - start_time) * 1000
        return ServiceHealth(
            status="healthy",
            response_time_ms=response_time,
            details={"host": "pgvector", "port": 5432}
        )
    except Exception as e:
        response_time = (time.time() - start_time) * 1000
        return ServiceHealth(
            status="unhealthy",
            response_time_ms=response_time,
            error=str(e)
        )


async def check_milvus() -> ServiceHealth:
    """Check Milvus vector database health."""
    start_time = time.time()
    
    try:
        from tools.vector_db.milvus_client_optimized import get_optimized_vector_db
        vector_db = get_optimized_vector_db()
        # Basic health check - try to get performance stats
        stats = vector_db.get_performance_stats()
        
        response_time = (time.time() - start_time) * 1000
        return ServiceHealth(
            status="healthy",
            response_time_ms=response_time,
            details={
                "collection": vector_db.collection_name,
                "uri": stats.get("uri", "unknown"),
                "total_queries": stats.get("total_queries", 0)
            }
        )
    except Exception as e:
        response_time = (time.time() - start_time) * 1000
        return ServiceHealth(
            status="unhealthy",
            response_time_ms=response_time,
            error=str(e)
        )


@router.get("/", response_model=HealthStatus)
async def health_check():
    """Comprehensive health check for all services."""
    timestamp = time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime())
    
    # Run health checks concurrently
    db_check, milvus_check = await asyncio.gather(
        check_database(),
        check_milvus(),
        return_exceptions=True
    )
    
    # Collect results
    services = {
        "database": db_check if isinstance(db_check, ServiceHealth) else ServiceHealth(
            status="unhealthy", response_time_ms=0, error=str(db_check)
        ),
        "milvus": milvus_check if isinstance(milvus_check, ServiceHealth) else ServiceHealth(
            status="unhealthy", response_time_ms=0, error=str(milvus_check)
        )
    }
    
    # Determine overall status
    all_healthy = all(service.status == "healthy" for service in services.values())
    overall_status = "healthy" if all_healthy else "degraded"
    
    return HealthStatus(
        status=overall_status,
        timestamp=timestamp,
        version="1.0.0",
        environment="development",
        services=services
    )


@router.get("/ready")
async def readiness_check():
    """Kubernetes readiness probe."""
    health_status = await health_check()
    
    if health_status.status == "healthy":
        return {"status": "ready"}
    else:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Service not ready"
        )


@router.get("/live")
async def liveness_check():
    """Kubernetes liveness probe."""
    return {
        "status": "alive",
        "timestamp": time.strftime("%Y-%m-%dT%H:%M:%SZ", time.gmtime())
    }

# Export the router for use in v1_router.py
health_router = router