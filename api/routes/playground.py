from agno.playground import Playground

from agents.document_agent import get_document_agent
from agents.document_analysis_agent import get_document_analysis_agent

######################################################
## Routes for the Playground Interface
######################################################

# Get specialized agents to serve in the playground
document_agent = get_document_agent(debug_mode=True)
document_analysis_agent = get_document_analysis_agent(debug_mode=True)

# Create a playground instance with specialized agents
playground = Playground(agents=[document_agent, document_analysis_agent])

# Get the router for the playground
playground_router = playground.get_async_router()
