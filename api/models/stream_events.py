"""
Stream event models for structured tool usage display.
"""
from enum import Enum
from typing import Dict, Any, Optional
from pydantic import BaseModel, Field
import json
import re


class EventType(str, Enum):
    """Types of streaming events."""
    THINKING = "thinking"
    TOOL_START = "tool_start"
    TOOL_END = "tool_end"
    CONTENT = "content"
    ERROR = "error"
    DONE = "done"


class StreamEvent(BaseModel):
    """Structured event for frontend consumption."""
    type: EventType
    data: Dict[str, Any] = Field(default_factory=dict)
    
    def to_sse(self) -> str:
        """Convert to SSE format."""
        return f"data: {json.dumps(self.dict())}\n\n"


def extract_tool_info(text: str) -> Optional[Dict[str, Any]]:
    """
    Extract tool information from Agno's tool call text.
    
    Example input:
    "[Calling tool: search_documents_vector with {'query': 'site diary', 'project_id': '51'}]"
    """
    # Pattern for tool calls
    tool_pattern = r"\[Calling tool: (\w+) with ({.*?})\]"
    match = re.search(tool_pattern, text)
    
    if match:
        tool_name = match.group(1)
        try:
            # Parse parameters (handle single quotes)
            params_str = match.group(2).replace("'", '"')
            params = json.loads(params_str)
        except:
            params = {}
        
        return {
            "name": tool_name,
            "params": params,
            "display_name": format_tool_display_name(tool_name),
            "icon": get_tool_icon(tool_name)
        }
    
    # Pattern for tool results
    result_pattern = r"\[Tool returned: (.*?)\]"
    match = re.search(result_pattern, text)
    if match:
        return {
            "type": "result",
            "content": match.group(1)
        }
    
    return None


def format_tool_display_name(tool_name: str) -> str:
    """Format tool name for display."""
    display_names = {
        "validate_date_requirement": "Validating Date Requirements",
        "parse_site_diary_dates": "Parsing Dates",
        "search_documents_vector": "Searching Documents",
        "search_similar_documents": "Finding Similar Documents",
        "get_vector_performance_stats": "Checking Performance"
    }
    return display_names.get(tool_name, tool_name.replace("_", " ").title())


def get_tool_icon(tool_name: str) -> str:
    """Get icon for tool display."""
    icons = {
        "validate_date_requirement": "📅",
        "parse_site_diary_dates": "📆",
        "search_documents_vector": "🔍",
        "search_similar_documents": "🔗",
        "get_vector_performance_stats": "📊"
    }
    return icons.get(tool_name, "🔧")


def parse_chunk_to_events(chunk_text: str) -> list[StreamEvent]:
    """
    Parse an Agno chunk into structured events.
    
    Returns a list of events as a chunk might contain multiple things.
    """
    events = []
    
    # Check for tool calls
    if "[Calling tool:" in chunk_text:
        tool_info = extract_tool_info(chunk_text)
        if tool_info and "name" in tool_info:
            events.append(StreamEvent(
                type=EventType.TOOL_START,
                data=tool_info
            ))
        # Remove the tool call from text for content processing
        chunk_text = re.sub(r"\[Calling tool:.*?\]", "", chunk_text)
    
    # Check for tool results
    if "[Tool returned:" in chunk_text:
        tool_info = extract_tool_info(chunk_text)
        if tool_info and tool_info.get("type") == "result":
            events.append(StreamEvent(
                type=EventType.TOOL_END,
                data={"result_preview": tool_info["content"][:100]}
            ))
        # Remove the tool result from text
        chunk_text = re.sub(r"\[Tool returned:.*?\]", "", chunk_text)
    
    # Any remaining text is content (preserve spaces)
    if chunk_text:  # Don't strip spaces - preserve them for proper formatting
        events.append(StreamEvent(
            type=EventType.CONTENT,
            data={"text": chunk_text}
        ))
    
    return events