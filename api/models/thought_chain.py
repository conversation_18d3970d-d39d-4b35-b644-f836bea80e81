"""
Pydantic models for ThoughtChain integration.
Defines the data structures for agent reasoning visualization.
"""
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum
from pydantic import BaseModel, Field, ConfigDict


class ThoughtStatus(str, Enum):
    """Status types for ThoughtChain items."""
    PENDING = "pending"
    RUNNING = "running" 
    SUCCESS = "success"
    ERROR = "error"


class ThoughtStepType(str, Enum):
    """Types of reasoning steps."""
    REASONING = "reasoning"          # Agent thinking/planning
    TOOL_CALL = "tool_call"          # Tool execution
    MEMORY_ACCESS = "memory_access"  # Memory retrieval
    DECISION = "decision"            # Decision making
    VALIDATION = "validation"        # Result validation
    ERROR_HANDLING = "error_handling" # Error recovery


class ThoughtChainItem(BaseModel):
    """Individual item in the thought chain matching Ant Design X structure."""
    
    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.isoformat() if v else None
        },
        # Ensure datetime fields are properly serialized
        arbitrary_types_allowed=True
    )
    
    # Core ThoughtChain properties
    title: str = Field(..., description="Step title/name")
    description: Optional[str] = Field(None, description="Step description")
    content: Optional[str] = Field(None, description="Detailed content or reasoning")
    status: ThoughtStatus = Field(..., description="Current status of this step")
    
    # Extended properties for agent reasoning
    step_type: ThoughtStepType = Field(..., description="Type of reasoning step")
    timestamp: datetime = Field(default_factory=datetime.now, description="When this step occurred")
    duration_ms: Optional[float] = Field(None, description="Execution time in milliseconds")
    
    # Tool-specific properties
    tool_name: Optional[str] = Field(None, description="Name of tool used (if applicable)")
    tool_args: Optional[Dict[str, Any]] = Field(None, description="Tool arguments (sanitized)")
    tool_result: Optional[str] = Field(None, description="Tool result summary")
    
    # Error handling
    error_message: Optional[str] = Field(None, description="Error message if status is error")
    retry_count: Optional[int] = Field(None, description="Number of retries attempted")
    
    # UI enhancements
    icon: Optional[str] = Field(None, description="Icon name for the step")
    extra: Optional[Dict[str, Any]] = Field(None, description="Additional UI data")
    confidence: Optional[float] = Field(None, description="Confidence score for this step")
    
    def to_json_dict(self) -> Dict[str, Any]:
        """Convert to JSON-serializable dictionary with proper datetime handling."""
        if hasattr(self, 'model_dump'):
            # Pydantic v2
            return self.model_dump(mode='json')
        else:
            # Pydantic v1 fallback
            data = self.dict()
            # Convert datetime fields manually
            if isinstance(data.get('timestamp'), datetime):
                data['timestamp'] = data['timestamp'].isoformat()
            return data


class AgentThoughtChain(BaseModel):
    """Complete thought chain for an agent's reasoning process."""
    
    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.isoformat() if v else None
        }
    )
    
    session_id: str = Field(..., description="Session identifier")
    agent_id: str = Field(..., description="Agent identifier")
    user_id: str = Field(..., description="User identifier")
    
    # Chain metadata
    chain_id: str = Field(..., description="Unique identifier for this thought chain")
    started_at: datetime = Field(default_factory=datetime.now, description="When reasoning started")
    completed_at: Optional[datetime] = Field(None, description="When reasoning completed")
    total_duration_ms: Optional[float] = Field(None, description="Total execution time")
    
    # Thought chain items
    items: List[ThoughtChainItem] = Field(default_factory=list, description="List of reasoning steps")
    
    # Overall status
    overall_status: ThoughtStatus = Field(default=ThoughtStatus.PENDING, description="Overall chain status")
    success: bool = Field(default=False, description="Whether the reasoning was successful")
    
    # Context
    user_message: str = Field(..., description="Original user message that triggered this reasoning")
    final_response: Optional[str] = Field(None, description="Agent's final response")
    
    # Metrics
    total_steps: int = Field(default=0, description="Total number of reasoning steps")
    tool_calls: int = Field(default=0, description="Number of tool calls made")
    memory_accesses: int = Field(default=0, description="Number of memory accesses")
    errors_encountered: int = Field(default=0, description="Number of errors encountered")
    
    def to_json_dict(self) -> Dict[str, Any]:
        """Convert to JSON-serializable dictionary with proper datetime handling."""
        if hasattr(self, 'model_dump'):
            # Pydantic v2
            return self.model_dump(mode='json')
        else:
            # Pydantic v1 fallback
            data = self.dict()
            # Convert datetime fields manually
            if isinstance(data.get('started_at'), datetime):
                data['started_at'] = data['started_at'].isoformat()
            if isinstance(data.get('completed_at'), datetime):
                data['completed_at'] = data['completed_at'].isoformat()
            # Handle nested ThoughtChainItems
            if 'items' in data:
                for item in data['items']:
                    if isinstance(item.get('timestamp'), datetime):
                        item['timestamp'] = item['timestamp'].isoformat()
            return data


class StreamingThoughtUpdate(BaseModel):
    """Streaming update for real-time thought chain visualization."""
    
    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.isoformat() if v else None
        }
    )
    
    chain_id: str = Field(..., description="Thought chain identifier")
    update_type: str = Field(..., description="Type of update: 'new_step', 'step_update', 'chain_complete'")
    
    # Step data (for new_step and step_update)
    step_data: Optional[ThoughtChainItem] = Field(None, description="Step data for updates")
    step_index: Optional[int] = Field(None, description="Index of the step being updated")
    
    # Chain completion data
    final_chain: Optional[AgentThoughtChain] = Field(None, description="Complete chain (for chain_complete)")
    
    # Metadata
    timestamp: datetime = Field(default_factory=datetime.now, description="Update timestamp")
    
    def to_json_dict(self) -> Dict[str, Any]:
        """Convert to JSON-serializable dictionary with proper datetime handling."""
        if hasattr(self, 'model_dump'):
            # Pydantic v2
            return self.model_dump(mode='json')
        else:
            # Pydantic v1 fallback
            data = self.dict()
            # Convert datetime fields manually
            if isinstance(data.get('timestamp'), datetime):
                data['timestamp'] = data['timestamp'].isoformat()
            # Handle nested objects
            if data.get('step_data') and hasattr(data['step_data'], 'to_json_dict'):
                data['step_data'] = data['step_data'].to_json_dict()
            if data.get('final_chain') and hasattr(data['final_chain'], 'to_json_dict'):
                data['final_chain'] = data['final_chain'].to_json_dict()
            return data


class ThoughtChainResponse(BaseModel):
    """HTTP response format that includes thought chain data."""
    
    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.isoformat() if v else None
        }
    )
    
    # Standard chat response
    message: str = Field(..., description="Agent's response message")
    session_id: str = Field(..., description="Session identifier")
    agent_id: str = Field(..., description="Agent identifier")
    
    # Thought chain integration
    thought_chain: Optional[AgentThoughtChain] = Field(None, description="Agent's reasoning process")
    
    # Response metadata
    response_time_ms: float = Field(..., description="Total response time")
    timestamp: datetime = Field(default_factory=datetime.now, description="Response timestamp")
    
    # Streaming support
    is_streaming: bool = Field(default=False, description="Whether response is being streamed")
    stream_complete: bool = Field(default=True, description="Whether streaming is complete")


class ThoughtChainConfig(BaseModel):
    """Configuration for thought chain capture and display."""
    
    # Capture settings
    enabled: bool = Field(default=True, description="Enable thought chain capture")
    capture_tool_args: bool = Field(default=True, description="Include tool arguments in chain")
    capture_tool_results: bool = Field(default=True, description="Include tool results in chain")
    capture_memory_access: bool = Field(default=True, description="Track memory accesses")
    capture_reasoning: bool = Field(default=True, description="Capture reasoning steps")
    
    # Privacy settings
    sanitize_sensitive_data: bool = Field(default=True, description="Remove sensitive data from chain")
    max_content_length: int = Field(default=500, description="Max length for content fields")
    
    # Performance settings
    max_chain_length: int = Field(default=100, description="Maximum number of steps in chain")
    enable_streaming: bool = Field(default=True, description="Enable real-time streaming updates")
    
    # UI settings
    default_size: str = Field(default="middle", description="Default ThoughtChain size")
    enable_collapse: bool = Field(default=True, description="Enable collapsible steps")
    show_timestamps: bool = Field(default=True, description="Show step timestamps")
    show_duration: bool = Field(default=True, description="Show step duration")