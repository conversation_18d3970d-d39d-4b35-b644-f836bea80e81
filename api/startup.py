"""
Application startup optimizations.
Preloads models and services to avoid cold starts.
"""
import logging
import os

logger = logging.getLogger(__name__)


async def startup_event():
    """
    FastAPI startup event handler.
    Preloads all optimized services for better first-request performance.
    """
    logger.info("🚀 Starting application initialization...")
    
    # Check if optimizations are enabled
    use_optimized = os.getenv("USE_OPTIMIZED_SERVICES", "true").lower() == "true"
    
    if use_optimized:
        try:
            logger.info("⚡ Preloading optimized services...")
            
            # Preload optimized embedding service
            from tools.vector_db.services.embedding_service_optimized import get_optimized_embedding_service
            embedding_service = get_optimized_embedding_service()
            logger.info("✅ Optimized embedding service preloaded")
            
            # Preload optimized vector database
            from tools.vector_db.milvus_client_optimized import get_optimized_vector_db
            get_optimized_vector_db()  # Initialize singleton
            logger.info("✅ Optimized vector database preloaded")
            
            # Warmup with sample query
            logger.info("🔥 Running warmup query...")
            test_embedding = embedding_service.generate_embedding("warmup query")
            if test_embedding:
                logger.info("✅ Warmup complete - services ready")
            
        except Exception as e:
            logger.error(f"❌ Failed to preload optimized services: {e}")
            logger.info("⚠️ Falling back to standard services")
    else:
        logger.info("📦 Using standard services (optimizations disabled)")
    
    logger.info("✅ Application initialization complete")


async def shutdown_event():
    """
    FastAPI shutdown event handler.
    Cleans up resources gracefully.
    """
    logger.info("🔚 Starting graceful shutdown...")
    
    try:
        # Shutdown optimized services if loaded
        use_optimized = os.getenv("USE_OPTIMIZED_SERVICES", "true").lower() == "true"
        
        if use_optimized:
            try:
                from tools.vector_db.services.embedding_service_optimized import get_optimized_embedding_service
                from tools.vector_db.milvus_client_optimized import get_optimized_vector_db
                
                # Save caches and close connections
                embedding_service = get_optimized_embedding_service()
                if hasattr(embedding_service, 'shutdown'):
                    embedding_service.shutdown()
                    logger.info("✅ Embedding service shutdown complete")
                
                vector_db = get_optimized_vector_db()
                if hasattr(vector_db, 'shutdown'):
                    vector_db.shutdown()
                    logger.info("✅ Vector database shutdown complete")
                    
            except Exception as e:
                logger.error(f"⚠️ Error during service shutdown: {e}")
    
    except Exception as e:
        logger.error(f"❌ Shutdown error: {e}")
    
    logger.info("✅ Graceful shutdown complete")