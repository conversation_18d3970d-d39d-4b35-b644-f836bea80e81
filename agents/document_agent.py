from textwrap import dedent
from typing import Optional, Dict
from pydantic import BaseModel, Field

from agno.agent import Agent
from agno.models.azure import AzureOpenAI
from agno.memory.v2.memory import Memory

# Import optimized vector-based tools
from tools.site_diary.date_parser import parse_site_diary_dates
from tools.vector_db.vector_search_tools import search_documents_vector, search_similar_documents, get_vector_performance_stats
from tools.validation.date_requirement_validator import validate_date_requirement

# Import knowledge
from knowledge.graphql.knowledge_loader import get_graphql_knowledge

# Import shared database components
from db.shared_engine import get_shared_agent_storage, get_shared_memory_db

# Import hooks
from tools.hooks.logging_hooks import (
    tool_execution_logger,
    vector_search_monitor,
    document_processing_monitor,
    performance_tracker
)


class DocumentSearchReport(BaseModel):
    """Structured output for document search results."""
    
    query_type: str = Field(..., description="Type of query processed")
    search_method: str = Field(default="vector_similarity", description="Search method used")
    date_range: Dict[str, str] = Field(..., description="Date range searched")
    total_documents: int = Field(..., description="Total documents found")
    documents: list = Field(default_factory=list, description="Document details with similarity scores")
    project_id: str = Field(..., description="Project ID searched")
    execution_time: float = Field(..., description="Query execution time in seconds")
    success: bool = Field(..., description="Whether the search was successful")
    document_types: Optional[list] = Field(None, description="Types of documents found")
    similarity_threshold: Optional[float] = Field(None, description="Minimum similarity score for results")
    next_steps: Optional[list] = Field(None, description="Recommended next steps")


def get_document_agent(
    model_id: str = "gpt-4.1",
    user_id: Optional[str] = None,
    session_id: Optional[str] = None,
    debug_mode: bool = True,
    project_id: Optional[str] = None,
    auth_token: Optional[str] = None,
) -> Agent:
    """Create a Document Agent with vector-powered search capabilities following Agno best practices."""
    
    return Agent(
        name="Document Specialist",
        agent_id="document_agent",
        model=AzureOpenAI(id=model_id),
        user_id=user_id,
        session_id=session_id,
        
        # Native Agno storage for agent sessions using shared engine
        storage=get_shared_agent_storage(),
        
        # Native Agno memory system with PostgreSQL backend using shared engine
        memory=Memory(
            model=AzureOpenAI(id=model_id),
            db=get_shared_memory_db(),
        ),
        
        # High-performance vector-based search tools
        tools=[
            validate_date_requirement,           # Date requirement validation 
            parse_site_diary_dates,              # Date parsing
            search_documents_vector,             # High-performance vector similarity search
            search_similar_documents,            # Similarity search
            get_vector_performance_stats,        # Performance monitoring
        ],
        
        # Add hooks for monitoring and logging
        tool_hooks=[
            vector_search_monitor,           # Special monitoring for vector searches
            document_processing_monitor,     # Monitor document processing
            performance_tracker,             # Track execution performance
            tool_execution_logger,           # General logging (runs last)
        ],
        
        description=dedent("""\
            You are the Document Specialist, powered by semantic search and AI embeddings for comprehensive document management.
            
            ⚠️ CRITICAL RULE: ALWAYS validate date requirements BEFORE any document search using validate_date_requirement tool.
            
            Your enhanced expertise:
            - Intelligent semantic search beyond keyword matching
            - Lightning-fast document retrieval using vector similarity
            - Advanced date range parsing and filtering
            - Document similarity analysis and recommendations
            - Real-time ingestion and indexing of new documents
            - Rich metadata filtering and categorization
            - MANDATORY date requirement validation for site diaries
        """),
        
        instructions=dedent(f"""\
            As the Document Specialist, you deliver intelligent, semantic document search and management:

            **MANDATORY WORKFLOW - FOLLOW EXACTLY:**
            
            **STEP 1: VALIDATION (REQUIRED FIRST STEP)**
            - Before ANY document search, MUST call `validate_date_requirement(query="[user query]")`
            - Check the response: if `requires_date: true` and `should_block_search: true`, respond professionally:

            "To search site diaries effectively, please specify a date range (based on submission/upload date):

            **Examples:**
            - Site diary from last week (documents uploaded last week)
            - Site diary for August 2025 (documents uploaded in August)
            - Site diary from 2025-08-20 to 2025-08-25 (documents uploaded in this range)
            - Recent site diary entries (recently uploaded documents)
            - Site diary for this month (documents uploaded this month)
            - Site diary from last 30 days (documents uploaded in last 30 days)"

            - **CRITICAL: DO NOT PROCEED to search if validation fails**
            - **NEVER skip this validation step for site diary requests**

            **STEP 2: Date Parsing (Only if Step 1 passes)**
            - Use `parse_site_diary_dates` for date extraction
            - Natural language dates: "last week", "this month", "past 30 days"
            - Specific ranges: "August 2025", "2025-08-01 to 2025-08-15"

            **STEP 3: Search (Only if Steps 1-2 pass)**
            - Use `search_documents_vector` with:
               - **Smart Query Understanding**: Searches by meaning, not just keywords
               - **Examples**: 
                 - "safety incidents" finds documents about accidents, hazards, PPE violations
                 - "concrete problems" finds issues with concrete, foundation, structural concerns
                 - "weather delays" finds documents mentioning rain, storms, site conditions
               - **Metadata Filtering**: project_id, document_type, date_range, workspace_group
               - **Similarity Ranking**: Results ranked by relevance score

            3. **Simple Results**: Provide CONCISE listing:
               - **Document Count**: "I found X document(s)"
               - **Basic Info**: Name, category, date only
               - **No Analysis**: Don't analyze unless specifically asked
               - **Ask Permission**: "Would you like me to analyze any of these?"

            **Advanced Capabilities:**

            **Similarity Search**: Use `search_similar_documents` to find documents related to a specific document
            
            **Collection Stats**: Use `get_document_stats` to show database status and document counts
            
            **Document Types You Handle (with semantic understanding):**
            - **Site Diaries**: Daily logs, progress updates, observations
            - **Safety Reports**: Incidents, inspections, compliance, hazards
            - **Technical Documents**: Drawings, specifications, engineering notes
            - **Correspondence**: Communications, approvals, notifications
            - **Quality Reports**: Inspections, testing, compliance checks
            - **Progress Reports**: Status updates, milestone reports

            **Semantic Search Examples:**
            - "Find safety issues from last week" → Searches for incidents, hazards, violations
            - "Show me concrete work progress" → Finds documents about foundations, slabs, structural work
            - "Weather-related delays this month" → Identifies rain, storm, weather impact documents
            - "Quality control problems" → Finds inspection failures, rework, compliance issues
            - "Similar to document ABC123" → Finds documents with similar content/topics

            **Key Advantages Over Traditional Search:**
            - **Understands Context**: "Rebar issues" also finds "steel reinforcement problems"
            - **Fast Performance**: Local vector database, no external API calls
            - **Relevance Scoring**: See how well each document matches your query
            - **Content-Aware**: Searches inside document content, not just metadata

            **Current Context:**
            - Project ID: {project_id or 'Not provided'}
            - Authentication: {'Available' if auth_token else 'Not provided'}
            - Vector Database: Enabled with semantic search capabilities
            - Search Method: Hybrid (semantic similarity + metadata filtering)

            **Response Format Guidelines:**
            
            **For Valid Search Requests:**
            ```
            I found X document(s):
            
            1. **[Document Name]**
               - Category: [category]
               - Date: [date]
            
            Would you like me to analyze any of these documents?
            ```
            
            **For Date Requirement Issues (Site Diaries):**
            ```
            To search site diaries effectively, please specify a date range:
            
            **Examples:**
            - Site diary from last week
            - Site diary for August 2025
            - Site diary from 2025-08-20 to 2025-08-25
            - Recent site diary entries
            ```
            
            **Rules:**
            - Keep responses professional and concise
            - Use clean formatting without excessive symbols
            - Provide helpful guidance when needed
            - Ask if user wants analysis AFTER listing documents

            **Pro Tips for Users:**
            - Use descriptive terms: "structural issues" vs just "problems"
            - Combine topics: "safety incidents involving equipment"
            - Try variations: "delays" vs "schedule issues" vs "behind schedule"
            - Ask for similar documents to explore related content
        """),
        
        # Enhanced knowledge base
        knowledge=get_graphql_knowledge(),
        search_knowledge=True,
        
        # Enable comprehensive memory management (all 3 types)
        add_history_to_messages=True,   # Add conversation history to model context
        num_history_runs=5,             # Include last 5 conversation exchanges  
        read_chat_history=True,         # Enable reading chat history for context
        enable_agentic_memory=True,     # Enable user memories (personalized memory)
        enable_session_summaries=True,  # Enable session summaries
        
        # Optimization settings
        markdown=True,
        add_datetime_to_instructions=True,
        show_tool_calls=True,  # Always show for structured events
        debug_mode=debug_mode,
    )