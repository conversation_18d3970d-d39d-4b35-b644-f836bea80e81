# ===========================================
# ENVIRONMENT SELECTION
# ===========================================
# Options: development, staging, production
ENVIRONMENT=development

# ===========================================
# DATABASE CONFIGURATION (Supabase)
# ===========================================
# Get these from your Supabase project settings
# Go to: Settings > Database > Connection string > URI

# Option 1: Use DATABASE_URL (recommended for Supabase)
# Copy the "Connection pooling" connection string from Supabase
# Format: postgresql://postgres.[project-ref]:[password]@aws-0-[region].pooler.supabase.com:6543/postgres?pgbouncer=true
DATABASE_URL=postgresql://postgres.xxxxxxxxxxxx:<EMAIL>:6543/postgres

# Option 2: Use individual settings (if not using DATABASE_URL)
# Get these from Supabase Settings > Database
DB_HOST=aws-0-us-east-1.pooler.supabase.com  # Use pooler URL for connection pooling
DB_PORT=6543  # 6543 for pooled connections (recommended), 5432 for direct
DB_USER=postgres.xxxxxxxxxxxx  # Your project reference
DB_PASSWORD=your-password-here
DB_NAME=postgres

# IMPORTANT: Enable pgvector extension in Supabase
# Run this SQL in Supabase SQL Editor:
# CREATE EXTENSION IF NOT EXISTS vector;

# ===========================================
# API KEYS
# ===========================================
# Note: OpenAI API keys no longer needed - using local all-MiniLM-L6-v2 model for embeddings
ANTHROPIC_API_KEY="your_anthropic_api_key_here"
AGNO_API_KEY="your_agno_api_key_here"

# ===========================================
# GRAPHQL API CONFIGURATION
# ===========================================
GRAPHQL_ENDPOINT=https://api.github.com/graphql
GITHUB_TOKEN=your_github_token_here
GRAPHQL_AUTH_TOKEN=your_api_token_here

# ===========================================
# MILVUS CONFIGURATION
# ===========================================

# Default: Use hosted Milvus instance
MILVUS_URI=https://milvus-stg.bina.cloud

# Alternative options:
# Development (Milvus Lite) - Uses local file-based database
# MILVUS_URI=./data/milvus_dev.db

# Local Milvus Standalone - Uses containerized Milvus
# MILVUS_URI=http://milvus:19530

# Optional: Authentication token for Milvus Cloud (leave empty for local/self-hosted)
# MILVUS_TOKEN=your_milvus_token_here

# Database name within Milvus (optional, defaults to "agno_knowledge")
MILVUS_DB_NAME=agno_knowledge

# ===========================================
# MINIO CONFIGURATION (for Milvus Standalone)
# ===========================================
# Used by Milvus for object storage in staging/production
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin

# ===========================================
# AGNO INTEGRATION SETTINGS
# ===========================================
# Enable debug mode for development
DEBUG_MODE=true

# Default collection name for Agno documents
AGNO_COLLECTION_NAME=agno_documents

# Embedding dimension (384 for all-MiniLM-L6-v2)
EMBEDDING_DIMENSION=384

# ===========================================
# PERFORMANCE TUNING (Optional)
# ===========================================
# Milvus search parameters
MILVUS_SEARCH_PARAMS_EF=256
MILVUS_INDEX_PARAMS_M=16
MILVUS_INDEX_PARAMS_EF_CONSTRUCTION=256

# Collection settings
MILVUS_CONSISTENCY_LEVEL=Strong
MILVUS_REPLICA_NUMBER=1

# ===========================================
# DOCKER IMAGE CONFIGURATION
# ===========================================
IMAGE_NAME=agent-api
IMAGE_TAG=latest