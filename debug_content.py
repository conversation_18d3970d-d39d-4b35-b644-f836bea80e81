#!/usr/bin/env python3
"""
Quick debug script to verify what content is being retrieved from the database.
"""
import os
import sys

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_content():
    """Debug content retrieval"""
    try:
        from tools.vector_db.milvus_client_optimized import get_optimized_vector_db
        from tools.vector_db.services.embedding_service_optimized import get_optimized_embedding_service
        
        print("🔍 Testing content retrieval from Milvus...")
        
        # Get services
        vector_db = get_optimized_vector_db()
        embedding_service = get_optimized_embedding_service()
        
        # Generate a simple query embedding
        query_embedding = embedding_service.generate_embedding("site diary")
        
        if not query_embedding:
            print("❌ Failed to generate embedding")
            return
        
        print(f"✅ Generated embedding with {len(query_embedding)} dimensions")
        
        # Search for documents
        print("🔍 Searching for documents in project 51...")
        documents = vector_db.search_documents(
            query_embedding=query_embedding,
            project_id="51",
            limit=3
        )
        
        print(f"📄 Found {len(documents)} documents")
        
        # Examine each document
        for i, doc in enumerate(documents):
            print(f"\n📋 Document {i+1}:")
            print(f"   Name: {doc.get('name', 'Unknown')}")
            print(f"   Document ID: {doc.get('document_id', 'Unknown')}")
            print(f"   Submitted At: {doc.get('submitted_at', 'Unknown')}")
            print(f"   Created At: {doc.get('created_at', 'Unknown')}")
            
            content_text = doc.get('content_text', '')
            print(f"   Content Text Length: {len(content_text)} characters")
            
            if content_text:
                # Look for actual dates in content
                import re
                
                # Extract first few lines for date checking
                lines = content_text.split('\n')[:10]
                print(f"   First few content lines:")
                for line_num, line in enumerate(lines):
                    if line.strip():
                        print(f"     {line_num+1}: {line.strip()[:100]}")
                
                # Look for date patterns
                date_patterns = [
                    r'TARIKH\s*:\s*(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{4})',
                    r'(\d{2}\/\d{2}\/\d{4})',
                    r'(\d{4}-\d{2}-\d{2})',
                ]
                
                dates_found = []
                for pattern in date_patterns:
                    matches = re.findall(pattern, content_text)
                    if matches:
                        dates_found.extend(matches)
                
                if dates_found:
                    print(f"   Dates found in content: {dates_found[:3]}")
                else:
                    print(f"   No clear dates found in content")
            else:
                print(f"   ❌ NO CONTENT TEXT AVAILABLE")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_content()