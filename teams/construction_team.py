from textwrap import dedent
from typing import Optional

from agno.team import Team
from agno.models.azure import AzureOpenAI

# Import specialized agents
from agents.document_agent import get_document_agent
from agents.document_analysis_agent import get_document_analysis_agent

# Import hooks
from tools.hooks.logging_hooks import (
    tool_execution_logger,
    performance_tracker
)

# Import shared database components
from db.shared_engine import get_shared_agent_storage


def get_construction_team(
    model_id: str = "gpt-4.1",
    user_id: Optional[str] = None,
    session_id: Optional[str] = None,
    debug_mode: bool = True,
    project_id: Optional[str] = None,
    auth_token: Optional[str] = None,
) -> Team:
    """Get the optimized Construction Team following Agno best practices.
    
    Args:
        model_id: Azure OpenAI model ID
        user_id: User ID for session management
        session_id: Session ID for conversation context
        debug_mode: Enable debug logging
        project_id: Project ID for shared state
        auth_token: Authentication token for API calls
    """
    
    # Create specialized team members
    document_agent = get_document_agent(
        model_id=model_id,
        user_id=user_id,
        session_id=session_id,
        debug_mode=debug_mode,
        project_id=project_id,
        auth_token=auth_token
    )
    
    document_analysis_agent = get_document_analysis_agent(
        model_id=model_id,
        user_id=user_id,
        session_id=session_id,
        debug_mode=debug_mode,
        project_id=project_id,
        auth_token=auth_token
    )
    
    # Create the Construction Team following Agno best practices
    return Team(
        name="ConstructPro AI Team",
        mode="route",
        model=AzureOpenAI(id=model_id),
        members=[
            document_agent,
            document_analysis_agent,
        ],
        # Concise description following Agno patterns
        description="Construction management team with document search and analysis specialists",
        
        # Explicit mutually exclusive routing to prevent dual delegation
        instructions=dedent("""\
            Route requests to EXACTLY ONE agent based on primary intent:

            **ANALYSIS REQUESTS → document_analysis_agent ONLY:**
            - Keywords: "analyze", "analyse", "review", "assess", "evaluate", "examine", "investigate", "summarize"
            - Examples: "analyse site diary for this week", "review safety reports", "assess progress"
            - Why: This agent handles search AND analysis internally - no dual routing needed

            **SEARCH/LISTING REQUESTS → document_agent ONLY:**  
            - Keywords: "find", "search", "show", "list", "get", "retrieve"
            - Examples: "find documents", "show me reports", "list site diaries"
            - Why: Simple document discovery without analysis

            **CRITICAL RULE: Never route to both agents simultaneously.**
            Always pass project_id and auth_token to maintain context.
        """),
        
        # Enable member response visibility (Agno best practice)
        show_members_responses=True,
        
        # Team-level hooks for monitoring
        tool_hooks=[
            performance_tracker,
            tool_execution_logger,
        ],
        
        # Team configuration  
        user_id=user_id,
        session_id=session_id,
        
        # Native Agno storage using shared engine
        storage=get_shared_agent_storage("construction_team_sessions"),
        
        # Optimized settings
        markdown=True,
        debug_mode=debug_mode,
    )
