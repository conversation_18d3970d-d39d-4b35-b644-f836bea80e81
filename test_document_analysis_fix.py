#!/usr/bin/env python3
"""
Test script to verify the document analysis agent uses real Milvus content instead of fabricating data.
Location: /Users/<USER>/Developer/gen-ai/bina-ai-agent-agno/test_document_analysis_fix.py
Purpose: Test the fixed document analysis agent to ensure it uses actual content from vector search
Why: To verify the agent no longer generates made-up analysis and uses real site diary data
RELEVANT FILES: agents/document_analysis_agent.py, tools/vector_db/current_output.md, tools/vector_db/vector_search_tools.py
"""

import asyncio
import logging
from agents.document_analysis_agent import get_document_analysis_agent

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_document_analysis_with_real_content():
    """Test the document analysis agent with a simple query to verify it uses real content."""
    
    print("🧪 Testing Document Analysis Agent - Real Content Usage")
    print("=" * 60)
    
    try:
        # Create the agent
        agent = get_document_analysis_agent(
            project_id="51",  # Using the project ID from the current_output.md
            debug_mode=True
        )
        
        # Test with a simple site diary query
        test_query = "Analyze the site diary documents for the current week"
        
        print(f"📝 Test Query: {test_query}")
        print("-" * 40)
        
        # Run the agent
        response = await agent.arun(test_query)
        
        print("🤖 Agent Response:")
        print("-" * 40)
        print(response.content)
        
        # Check if the response contains real data from the Milvus content
        real_data_indicators = [
            "24/02/2025",  # Real date from the documents
            "23/02/2025",  # Real date from the documents  
            "STE-01",      # Real equipment ID
            "STLGH-01",    # Real equipment ID
            "Coarse Sand", # Real material
            "G25JKR",      # Real concrete type
            "Subgrade level preparation",  # Real activity
            "CBR In-Situ", # Real activity
        ]
        
        fake_data_indicators = [
            "20 (10 laborers, 5 supervisors, 5 operators)",  # Made-up worker breakdown
            "18 (9 laborers, 4 supervisors, 5 operators)",   # Made-up worker breakdown
            "Site preparation from 8:00 AM to 5:00 PM",      # Made-up time range
            "No delays were reported",                        # Made-up status
        ]
        
        real_data_found = sum(1 for indicator in real_data_indicators if indicator in response.content)
        fake_data_found = sum(1 for indicator in fake_data_indicators if indicator in response.content)
        
        print("\n" + "=" * 60)
        print("📊 ANALYSIS RESULTS:")
        print(f"✅ Real data indicators found: {real_data_found}/{len(real_data_indicators)}")
        print(f"❌ Fake data indicators found: {fake_data_found}/{len(fake_data_indicators)}")
        
        if real_data_found > 0 and fake_data_found == 0:
            print("🎉 SUCCESS: Agent is using real content from Milvus!")
        elif real_data_found > 0 and fake_data_found > 0:
            print("⚠️  MIXED: Agent uses some real content but still fabricates some data")
        elif fake_data_found > 0:
            print("❌ FAILURE: Agent is still fabricating data instead of using real content")
        else:
            print("❓ UNCLEAR: Could not determine if agent is using real or fake data")
            
        print("\nReal data indicators checked:")
        for indicator in real_data_indicators:
            found = "✅" if indicator in response.content else "❌"
            print(f"  {found} {indicator}")
            
        print("\nFake data indicators checked:")
        for indicator in fake_data_indicators:
            found = "❌" if indicator in response.content else "✅"
            print(f"  {found} {indicator}")
        
    except Exception as e:
        logger.error(f"❌ Test failed with error: {e}")
        print(f"❌ Test failed: {e}")

if __name__ == "__main__":
    asyncio.run(test_document_analysis_with_real_content())
