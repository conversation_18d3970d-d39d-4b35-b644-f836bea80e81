"""Create chat system tables

Revision ID: 001
Revises: 
Create Date: 2025-08-24

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import UUID

# revision identifiers, used by Alembic.
revision = '001'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Create chat system tables with proper indexes and constraints."""
    
    # Create chat_sessions table
    op.create_table(
        'chat_sessions',
        sa.Column('id', UUID(as_uuid=True), primary_key=True),
        sa.Column('user_id', sa.String(255), nullable=False),
        sa.Column('title', sa.String(500), nullable=False, default='New Chat'),
        sa.Column('project_id', sa.String(255), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
        sa.Column('message_count', sa.Integer, default=0, nullable=False),
        sa.Column('last_message_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('is_deleted', sa.Boolean, default=False, nullable=False),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    )
    
    # Create indexes for chat_sessions
    op.create_index('idx_chat_sessions_id', 'chat_sessions', ['id'])
    op.create_index('idx_chat_sessions_user_created', 'chat_sessions', ['user_id', 'created_at'])
    op.create_index('idx_chat_sessions_user_updated', 'chat_sessions', ['user_id', 'updated_at'])
    op.create_index('idx_chat_sessions_user_active', 'chat_sessions', ['user_id', 'is_deleted'])
    op.create_index('idx_chat_sessions_project_active', 'chat_sessions', ['project_id', 'is_deleted'])
    op.create_index('idx_chat_sessions_project', 'chat_sessions', ['project_id'])
    op.create_index('idx_chat_sessions_deleted', 'chat_sessions', ['is_deleted'])

    # Create chat_messages table
    op.create_table(
        'chat_messages',
        sa.Column('id', UUID(as_uuid=True), primary_key=True),
        sa.Column('session_id', UUID(as_uuid=True), sa.ForeignKey('chat_sessions.id', ondelete='CASCADE'), nullable=False),
        sa.Column('user_id', sa.String(255), nullable=False),
        sa.Column('role', sa.String(20), nullable=False),
        sa.Column('content', sa.Text, nullable=False),
        sa.Column('agent_id', sa.String(100), nullable=True),
        sa.Column('model_id', sa.String(100), nullable=True),
        sa.Column('token_count', sa.Integer, nullable=True),
        sa.Column('response_time_ms', sa.Integer, nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
        sa.Column('is_deleted', sa.Boolean, default=False, nullable=False),
        sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    )
    
    # Create indexes for chat_messages
    op.create_index('idx_chat_messages_id', 'chat_messages', ['id'])
    op.create_index('idx_chat_messages_session_created', 'chat_messages', ['session_id', 'created_at'])
    op.create_index('idx_chat_messages_session_active', 'chat_messages', ['session_id', 'is_deleted'])
    op.create_index('idx_chat_messages_user_created', 'chat_messages', ['user_id', 'created_at'])
    op.create_index('idx_chat_messages_role_created', 'chat_messages', ['role', 'created_at'])
    op.create_index('idx_chat_messages_session', 'chat_messages', ['session_id'])
    op.create_index('idx_chat_messages_user', 'chat_messages', ['user_id'])
    op.create_index('idx_chat_messages_role', 'chat_messages', ['role'])
    op.create_index('idx_chat_messages_deleted', 'chat_messages', ['is_deleted'])

    # Create chat_session_stats table
    op.create_table(
        'chat_session_stats',
        sa.Column('id', UUID(as_uuid=True), primary_key=True),
        sa.Column('session_id', UUID(as_uuid=True), sa.ForeignKey('chat_sessions.id', ondelete='CASCADE'), nullable=False, unique=True),
        sa.Column('user_id', sa.String(255), nullable=False),
        sa.Column('total_messages', sa.Integer, default=0, nullable=False),
        sa.Column('user_messages', sa.Integer, default=0, nullable=False),
        sa.Column('ai_messages', sa.Integer, default=0, nullable=False),
        sa.Column('system_messages', sa.Integer, default=0, nullable=False),
        sa.Column('total_tokens', sa.Integer, nullable=True),
        sa.Column('user_tokens', sa.Integer, nullable=True),
        sa.Column('ai_tokens', sa.Integer, nullable=True),
        sa.Column('avg_response_time_ms', sa.Integer, nullable=True),
        sa.Column('total_response_time_ms', sa.Integer, nullable=True),
        sa.Column('session_duration_seconds', sa.Integer, nullable=True),
        sa.Column('first_message_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('last_message_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
    )
    
    # Create indexes for chat_session_stats
    op.create_index('idx_chat_session_stats_session', 'chat_session_stats', ['session_id'])
    op.create_index('idx_chat_session_stats_user', 'chat_session_stats', ['user_id'])
    op.create_index('idx_chat_session_stats_updated', 'chat_session_stats', ['updated_at'])
    op.create_index('idx_chat_session_stats_duration', 'chat_session_stats', ['session_duration_seconds'])
    op.create_index('idx_chat_session_stats_tokens', 'chat_session_stats', ['total_tokens'])

    # Create trigger to update chat_sessions.updated_at
    op.execute("""
        CREATE OR REPLACE FUNCTION update_chat_session_updated_at()
        RETURNS TRIGGER AS $$
        BEGIN
            NEW.updated_at = NOW();
            RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;
    """)
    
    op.execute("""
        CREATE TRIGGER trigger_update_chat_session_updated_at
            BEFORE UPDATE ON chat_sessions
            FOR EACH ROW
            EXECUTE FUNCTION update_chat_session_updated_at();
    """)


def downgrade() -> None:
    """Drop chat system tables and related objects."""
    
    # Drop triggers and functions
    op.execute("DROP TRIGGER IF EXISTS trigger_update_chat_session_updated_at ON chat_sessions;")
    op.execute("DROP FUNCTION IF EXISTS update_chat_session_updated_at();")
    
    # Drop tables in reverse dependency order
    op.drop_table('chat_session_stats')
    op.drop_table('chat_messages') 
    op.drop_table('chat_sessions')