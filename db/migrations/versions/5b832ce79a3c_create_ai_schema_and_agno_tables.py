"""Create AI schema and Agno tables

Revision ID: 5b832ce79a3c
Revises: 001
Create Date: 2025-09-03 11:43:33.900813

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import UUID


# revision identifiers, used by Alembic.
revision = '5b832ce79a3c'
down_revision = '001'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Create AI schema and Agno-related tables."""
    
    # Create AI schema
    op.execute('CREATE SCHEMA IF NOT EXISTS ai')
    
    # Create agent_sessions table in ai schema
    op.create_table(
        'agent_sessions',
        sa.Column('session_id', UUID(as_uuid=True), primary_key=True),
        sa.Column('user_id', sa.String(255), nullable=False),
        sa.Column('agent_id', sa.String(255), nullable=True),
        sa.Column('project_id', sa.String(255), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
        sa.Column('agent_metadata', sa.Text, nullable=True),
        schema='ai'
    )
    
    # Create indexes for agent_sessions
    op.create_index('idx_agent_sessions_user_id', 'agent_sessions', ['user_id'], schema='ai')
    op.create_index('idx_agent_sessions_project_id', 'agent_sessions', ['project_id'], schema='ai')
    op.create_index('idx_agent_sessions_updated_at', 'agent_sessions', ['updated_at'], schema='ai')
    
    # Create user_memories table in ai schema
    op.create_table(
        'user_memories',
        sa.Column('id', UUID(as_uuid=True), primary_key=True),
        sa.Column('user_id', sa.String(255), nullable=False),
        sa.Column('session_id', UUID(as_uuid=True), nullable=True),
        sa.Column('agent_id', sa.String(255), nullable=True),
        sa.Column('memory_type', sa.String(50), default='conversation', nullable=False),
        sa.Column('content', sa.Text, nullable=False),
        sa.Column('embedding', sa.String, nullable=True),  # Vector embedding
        sa.Column('memory_metadata', sa.Text, nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
        schema='ai'
    )
    
    # Create indexes for user_memories
    op.create_index('idx_user_memories_user_id', 'user_memories', ['user_id'], schema='ai')
    op.create_index('idx_user_memories_session_id', 'user_memories', ['session_id'], schema='ai')
    
    # Create agent_storage table in ai schema
    op.create_table(
        'agent_storage',
        sa.Column('id', UUID(as_uuid=True), primary_key=True),
        sa.Column('agent_id', sa.String(255), nullable=False),
        sa.Column('user_id', sa.String(255), nullable=True),
        sa.Column('session_id', UUID(as_uuid=True), nullable=True),
        sa.Column('storage_key', sa.String(255), nullable=False),
        sa.Column('storage_value', sa.Text, nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.func.now(), nullable=False),
        schema='ai'
    )
    
    # Create indexes for agent_storage
    op.create_index('idx_agent_storage_agent_id', 'agent_storage', ['agent_id'], schema='ai')
    op.create_index('idx_agent_storage_session_id', 'agent_storage', ['session_id'], schema='ai')
    
    # Add foreign key constraints
    op.create_foreign_key(
        'fk_user_memories_session_id',
        'user_memories', 'agent_sessions',
        ['session_id'], ['session_id'],
        source_schema='ai', referent_schema='ai',
        ondelete='CASCADE'
    )
    
    op.create_foreign_key(
        'fk_agent_storage_session_id',
        'agent_storage', 'agent_sessions',
        ['session_id'], ['session_id'],
        source_schema='ai', referent_schema='ai',
        ondelete='CASCADE'
    )
    
    # Create update triggers for updated_at columns
    op.execute("""
        CREATE OR REPLACE FUNCTION ai.update_updated_at_column()
        RETURNS TRIGGER AS $$
        BEGIN
            NEW.updated_at = NOW();
            RETURN NEW;
        END;
        $$ language 'plpgsql';
    """)
    
    # Create triggers for each table
    op.execute("""
        CREATE TRIGGER update_agent_sessions_updated_at
        BEFORE UPDATE ON ai.agent_sessions
        FOR EACH ROW EXECUTE FUNCTION ai.update_updated_at_column();
    """)
    
    op.execute("""
        CREATE TRIGGER update_user_memories_updated_at
        BEFORE UPDATE ON ai.user_memories
        FOR EACH ROW EXECUTE FUNCTION ai.update_updated_at_column();
    """)
    
    op.execute("""
        CREATE TRIGGER update_agent_storage_updated_at
        BEFORE UPDATE ON ai.agent_storage
        FOR EACH ROW EXECUTE FUNCTION ai.update_updated_at_column();
    """)


def downgrade() -> None:
    """Drop AI schema and all Agno tables."""
    
    # Drop triggers first
    op.execute("DROP TRIGGER IF EXISTS update_agent_storage_updated_at ON ai.agent_storage")
    op.execute("DROP TRIGGER IF EXISTS update_user_memories_updated_at ON ai.user_memories")
    op.execute("DROP TRIGGER IF EXISTS update_agent_sessions_updated_at ON ai.agent_sessions")
    op.execute("DROP FUNCTION IF EXISTS ai.update_updated_at_column()")
    
    # Drop tables in reverse order of dependencies
    op.drop_table('agent_storage', schema='ai')
    op.drop_table('user_memories', schema='ai')
    op.drop_table('agent_sessions', schema='ai')
    
    # Drop schema
    op.execute('DROP SCHEMA IF EXISTS ai CASCADE')