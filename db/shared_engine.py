"""Shared database engine for Agno agents to prevent connection pool exhaustion."""

from agno.storage.agent.postgres import PostgresAgentStorage
from agno.memory.v2.db.postgres import PostgresMemoryDb

from db.session import db_engine, db_url


def get_shared_agent_storage(table_name: str = "agent_sessions", schema: str = "ai") -> PostgresAgentStorage:
    """Get shared PostgresAgentStorage instance using the global db_engine."""
    return PostgresAgentStorage(
        table_name=table_name,
        schema=schema,
        db_url=db_url,
        db_engine=db_engine  # Use shared engine
    )


def get_shared_memory_db(table_name: str = "user_memories", schema: str = "ai") -> PostgresMemoryDb:
    """Get shared PostgresMemoryDb instance using the global db_engine."""
    return PostgresMemoryDb(
        table_name=table_name,
        schema=schema,
        db_url=db_url,
        db_engine=db_engine  # Use shared engine
    )