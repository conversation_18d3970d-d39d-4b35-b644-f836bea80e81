"""
Database models for the Agno AI Agent API.

This module contains SQLAlchemy models for the chat system and agent storage.
"""

from sqlalchemy import Column, <PERSON>, Integer, DateTime, Boolean, Text, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid

Base = declarative_base()


class ChatSession(Base):
    """Model for chat sessions."""
    
    __tablename__ = 'chat_sessions'
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(String(255), nullable=False, index=True)
    title = Column(String(500), nullable=False, default='New Chat')
    project_id = Column(String(255), nullable=True, index=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    message_count = Column(Integer, default=0, nullable=False)
    last_message_at = Column(DateTime(timezone=True), nullable=True)
    is_deleted = Column(Boolean, default=False, nullable=False)
    deleted_at = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    messages = relationship("ChatMessage", back_populates="session", cascade="all, delete-orphan")
    stats = relationship("ChatSessionStats", back_populates="session", uselist=False, cascade="all, delete-orphan")


class ChatMessage(Base):
    """Model for chat messages."""
    
    __tablename__ = 'chat_messages'
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    session_id = Column(UUID(as_uuid=True), ForeignKey('chat_sessions.id', ondelete='CASCADE'), nullable=False, index=True)
    user_id = Column(String(255), nullable=False, index=True)
    role = Column(String(20), nullable=False, index=True)  # 'user', 'assistant', 'system'
    content = Column(Text, nullable=False)
    agent_id = Column(String(100), nullable=True)
    model_id = Column(String(100), nullable=True)
    token_count = Column(Integer, nullable=True)
    response_time_ms = Column(Integer, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    is_deleted = Column(Boolean, default=False, nullable=False)
    deleted_at = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    session = relationship("ChatSession", back_populates="messages")


class ChatSessionStats(Base):
    """Model for chat session statistics."""
    
    __tablename__ = 'chat_session_stats'
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    session_id = Column(UUID(as_uuid=True), ForeignKey('chat_sessions.id', ondelete='CASCADE'), nullable=False, unique=True)
    user_id = Column(String(255), nullable=False, index=True)
    total_messages = Column(Integer, default=0, nullable=False)
    user_messages = Column(Integer, default=0, nullable=False)
    ai_messages = Column(Integer, default=0, nullable=False)
    system_messages = Column(Integer, default=0, nullable=False)
    total_tokens = Column(Integer, nullable=True)
    user_tokens = Column(Integer, nullable=True)
    ai_tokens = Column(Integer, nullable=True)
    avg_response_time_ms = Column(Integer, nullable=True)
    total_response_time_ms = Column(Integer, nullable=True)
    session_duration_seconds = Column(Integer, nullable=True)
    first_message_at = Column(DateTime(timezone=True), nullable=True)
    last_message_at = Column(DateTime(timezone=True), nullable=True)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    # Relationships
    session = relationship("ChatSession", back_populates="stats")


# For Agno framework compatibility - these tables are created by Agno automatically
# but we define them here for reference and Alembic migrations

class AgentSession(Base):
    """Model for Agno agent sessions."""
    
    __tablename__ = 'agent_sessions'
    __table_args__ = {'schema': 'ai'}
    
    session_id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(String(255), nullable=False, index=True)
    agent_id = Column(String(255), nullable=True)
    project_id = Column(String(255), nullable=True, index=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    agent_metadata = Column(Text, nullable=True)  # JSON metadata


class UserMemory(Base):
    """Model for Agno user memories."""
    
    __tablename__ = 'user_memories'
    __table_args__ = {'schema': 'ai'}
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(String(255), nullable=False, index=True)
    session_id = Column(UUID(as_uuid=True), ForeignKey('ai.agent_sessions.session_id', ondelete='CASCADE'), nullable=True)
    agent_id = Column(String(255), nullable=True)
    memory_type = Column(String(50), default='conversation', nullable=False)
    content = Column(Text, nullable=False)
    embedding = Column(String, nullable=True)  # Vector embedding (handled by pgvector)
    memory_metadata = Column(Text, nullable=True)  # JSON metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)


class AgentStorage(Base):
    """Model for Agno agent storage."""
    
    __tablename__ = 'agent_storage'
    __table_args__ = {'schema': 'ai'}
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    agent_id = Column(String(255), nullable=False, index=True)
    user_id = Column(String(255), nullable=True)
    session_id = Column(UUID(as_uuid=True), ForeignKey('ai.agent_sessions.session_id', ondelete='CASCADE'), nullable=True)
    storage_key = Column(String(255), nullable=False)
    storage_value = Column(Text, nullable=False)  # JSON storage value
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)