from os import getenv


def get_db_url() -> str:
    # First check if DATABASE_URL is provided (Supabase recommended approach)
    database_url = getenv("DATABASE_URL")
    if database_url:
        # Force using psycopg instead of psycopg2
        if database_url.startswith("postgresql://"):
            database_url = database_url.replace("postgresql://", "postgresql+psycopg://")
        return database_url
    
    # Fallback to individual components
    db_driver = getenv("DB_DRIVER", "postgresql+psycopg")
    db_user = getenv("DB_USER")
    db_pass = getenv("DB_PASS")
    db_host = getenv("DB_HOST")
    db_port = getenv("DB_PORT", "6543")  # Default to Supabase pooled connection port
    db_database = getenv("DB_DATABASE", "postgres")
    
    if not all([db_user, db_host]):
        raise ValueError(
            "Database configuration missing. Please set DATABASE_URL or "
            "DB_USER and DB_HOST environment variables for Supabase connection."
        )
    
    return "{}://{}{}@{}:{}/{}".format(
        db_driver,
        db_user,
        f":{db_pass}" if db_pass else "",
        db_host,
        db_port,
        db_database,
    )
