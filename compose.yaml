services:
  # PostgreSQL removed - using Supabase cloud instead
  # Configure Supabase connection in .env file:
  # DATABASE_URL=postgresql://[user]:[password]@[host]:[port]/[database]
  
  # Milvus dependencies
  milvus-etcd:
    container_name: milvus-etcd
    image: quay.io/coreos/etcd:v3.5.5
    environment:
      - ETCD_AUTO_COMPACTION_MODE=revision
      - ETCD_AUTO_COMPACTION_RETENTION=1000
      - ETCD_QUOTA_BACKEND_BYTES=**********
      - ETCD_SNAPSHOT_COUNT=50000
    volumes:
      - etcd_data:/etcd
    command: etcd -advertise-client-urls=http://127.0.0.1:2379 -listen-client-urls http://0.0.0.0:2379 --data-dir /etcd
    healthcheck:
      test: ["CMD", "etcdctl", "endpoint", "health"]
      interval: 30s
      timeout: 20s
      retries: 3
    networks:
      - agent-api

  milvus-minio:
    container_name: milvus-minio
    image: minio/minio:RELEASE.2023-03-20T20-16-18Z
    environment:
      MINIO_ACCESS_KEY: minioadmin
      MINIO_SECRET_KEY: minioadmin
    ports:
      - "9001:9001"
      - "9000:9000"
    volumes:
      - minio_data:/minio_data
    command: minio server /minio_data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    networks:
      - agent-api

  # Milvus vector database
  milvus:
    container_name: milvus-standalone
    image: milvusdb/milvus:v2.4.1
    command: ["milvus", "run", "standalone"]
    security_opt:
      - seccomp:unconfined
    environment:
      ETCD_ENDPOINTS: milvus-etcd:2379
      MINIO_ADDRESS: milvus-minio:9000
    volumes:
      - milvus_data:/var/lib/milvus
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9091/healthz"]
      interval: 30s
      start_period: 90s
      timeout: 20s
      retries: 3
    ports:
      - "19530:19530"
      - "9091:9091"
    depends_on:
      - "milvus-etcd"
      - "milvus-minio"
    networks:
      - agent-api

  # Attu - Milvus management GUI
  attu:
    container_name: attu
    image: zilliz/attu:latest
    environment:
      MILVUS_URL: milvus:19530
      HOST_URL: http://127.0.0.1:8002
    ports:
      - "8002:3000"
    depends_on:
      - milvus
    networks:
      - agent-api
    extra_hosts:
      - "host.docker.internal:host-gateway"

  api:
    build:
      context: .
      dockerfile: Dockerfile
    image: ${IMAGE_NAME:-agent-api}:${IMAGE_TAG:-latest}
    command: uvicorn api.main:app --host 0.0.0.0 --port 8000 --reload
    restart: unless-stopped
    ports:
      - "8000:8000"
    volumes:
      - .:/app
    environment:
      # OpenAI Configuration
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      # Azure OpenAI Configuration
      AZURE_OPENAI_API_KEY: ${AZURE_OPENAI_API_KEY}
      AZURE_OPENAI_ENDPOINT: ${AZURE_OPENAI_ENDPOINT}
      AZURE_OPENAI_API_VERSION: ${AZURE_OPENAI_API_VERSION}
      AZURE_OPENAI_DEPLOYMENT: ${AZURE_OPENAI_DEPLOYMENT}
      # GraphQL Configuration
      GRAPHQL_ENDPOINT: ${GRAPHQL_ENDPOINT:-http://localhost:3000/graphql}
      # Database Configuration (Supabase Cloud)
      DATABASE_URL: ${DATABASE_URL}
      DB_HOST: ${DB_HOST}
      DB_PORT: ${DB_PORT:-6543}
      DB_USER: ${DB_USER}
      DB_PASS: ${DB_PASSWORD}
      DB_DATABASE: ${DB_NAME:-postgres}
      WAIT_FOR_DB: ${WAIT_FOR_DB:-False}
      RUN_MIGRATIONS: ${RUN_MIGRATIONS:-True}
      PRINT_ENV_ON_LOAD: "True"
      # Milvus Configuration
      MILVUS_URI: ${MILVUS_URI}
      # Performance Optimizations
      USE_OPTIMIZED_SERVICES: "true"
      USE_OPTIMIZED_EMBEDDING: "true"
      USE_OPTIMIZED_VECTOR_DB: "true"
      EMBEDDING_CACHE_SIZE: "5000"
      MILVUS_POOL_SIZE: "5"
      # Logfire Configuration
      LOGFIRE_TOKEN: ${LOGFIRE_TOKEN}
      LOGFIRE_PROJECT_NAME: ${LOGFIRE_PROJECT_NAME:-bina-agent-api}
      LOGFIRE_SERVICE_NAME: agent-api
      LOGFIRE_ENVIRONMENT: ${ENVIRONMENT:-development}
      LOGFIRE_DISABLED: ${LOGFIRE_DISABLED:-false}
    networks:
      - agent-api
    depends_on:
      - milvus
    extra_hosts:
      - "host.docker.internal:host-gateway"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 40s

networks:
  agent-api:

volumes:
  etcd_data:
  minio_data:
  milvus_data:
