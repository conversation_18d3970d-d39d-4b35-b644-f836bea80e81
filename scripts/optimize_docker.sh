#!/bin/bash

# Docker Image Optimization Script
# Builds and compares different Docker image optimization strategies

set -e

echo "🚀 Docker Image Optimization Script"
echo "======================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Image names
ORIGINAL_IMAGE="agno-agent:original"
OPTIMIZED_IMAGE="agno-agent:optimized"

# Function to get image size
get_image_size() {
    local image=$1
    docker images --format "table {{.Size}}" $image | tail -n +2
}

# Function to build and measure
build_and_measure() {
    local dockerfile=$1
    local image_name=$2
    local description=$3
    
    echo -e "\n${BLUE}Building $description...${NC}"
    echo "Dockerfile: $dockerfile"
    echo "Image: $image_name"
    
    start_time=$(date +%s)
    
    if [ "$dockerfile" = "Dockerfile" ]; then
        docker build -t $image_name .
    else
        docker build -f $dockerfile -t $image_name .
    fi
    
    end_time=$(date +%s)
    build_time=$((end_time - start_time))
    
    size=$(get_image_size $image_name)
    
    echo -e "${GREEN}✅ Build completed${NC}"
    echo "  📏 Size: $size"
    echo "  ⏱️  Build time: ${build_time}s"
    
    # Store results
    echo "$image_name,$size,$build_time,$description" >> docker_optimization_results.csv
}

# Initialize results file
echo "Image,Size,Build Time (s),Description" > docker_optimization_results.csv

echo -e "\n${YELLOW}Starting Docker image optimization comparison...${NC}"

# Build original image
echo -e "\n${BLUE}1. Building Original Image${NC}"
build_and_measure "Dockerfile" "$ORIGINAL_IMAGE" "Original Dockerfile"

# Build optimized image  
echo -e "\n${BLUE}2. Building Optimized Image (Multi-stage)${NC}"
build_and_measure "Dockerfile.optimized" "$OPTIMIZED_IMAGE" "Multi-stage optimized"

# Display results
echo -e "\n${GREEN}📊 Optimization Results${NC}"
echo "======================="

echo -e "\n${BLUE}Size Comparison:${NC}"
docker images --format "table {{.Repository}}:{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}" agno-agent

# Calculate savings
original_size_bytes=$(docker inspect $ORIGINAL_IMAGE --format='{{.Size}}' 2>/dev/null || echo "0")
optimized_size_bytes=$(docker inspect $OPTIMIZED_IMAGE --format='{{.Size}}' 2>/dev/null || echo "0")

if [ "$original_size_bytes" != "0" ] && [ "$optimized_size_bytes" != "0" ]; then
    savings=$((100 - (optimized_size_bytes * 100 / original_size_bytes)))
    echo -e "\n${GREEN}💾 Space Savings: ${savings}% reduction${NC}"
fi

# Layer analysis
echo -e "\n${BLUE}📋 Layer Analysis${NC}"
echo "Original image layers:"
docker history $ORIGINAL_IMAGE --format "table {{.Size}}\t{{.CreatedBy}}" 2>/dev/null | head -10

echo -e "\nOptimized image layers:"
docker history $OPTIMIZED_IMAGE --format "table {{.Size}}\t{{.CreatedBy}}" 2>/dev/null | head -10

# Performance test recommendations
echo -e "\n${YELLOW}🏃 Performance Testing Recommendations:${NC}"
echo "1. Test startup time: docker run --rm $OPTIMIZED_IMAGE"
echo "2. Test memory usage: docker stats $OPTIMIZED_IMAGE"
echo "3. Test API endpoints: curl http://localhost:8000/v1/health"
echo "4. Run load tests against optimized image"

# Cleanup options
echo -e "\n${BLUE}🧹 Cleanup${NC}"
echo "To remove test images:"
echo "  docker rmi $ORIGINAL_IMAGE $OPTIMIZED_IMAGE 2>/dev/null || true"

echo -e "\n${GREEN}✅ Docker optimization analysis complete!${NC}"
echo "Results saved to: docker_optimization_results.csv"

# Summary recommendations
echo -e "\n${YELLOW}📝 Recommendations:${NC}"
echo "1. Use Dockerfile.optimized for production (best balance of size/complexity)"
echo "2. Update CI/CD pipeline to use optimized Dockerfile"
echo "3. Monitor startup times and memory usage in production"
echo "4. Regularly update base images for security patches"
echo ""
echo "To implement:"
echo "  cp Dockerfile.optimized Dockerfile"
echo "  cp .dockerignore.optimized .dockerignore"
echo "  docker build -t agno-agent:latest ."
