#!/bin/bash

############################################################################
# Stop Docker Container Script
############################################################################

set -e

CONTAINER_NAME="${CONTAINER_NAME:-agent-api}"

echo "🛑 Stopping Agno AI Agent API container..."

if docker ps --filter name=${CONTAINER_NAME} --format '{{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
    docker stop ${CONTAINER_NAME}
    docker rm ${CONTAINER_NAME}
    echo "✅ Container stopped and removed successfully!"
else
    echo "ℹ️  Container ${CONTAINER_NAME} is not running."
fi