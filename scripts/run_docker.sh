#!/bin/bash

############################################################################
# Standalone Docker Run Script for Agno AI Agent API
# Usage: ./scripts/run_docker.sh [dev|prod]
############################################################################

set -e

# Configuration
IMAGE_NAME="${IMAGE_NAME:-agent-api}"
IMAGE_TAG="${IMAGE_TAG:-latest}"
CONTAINER_NAME="${CONTAINER_NAME:-agent-api}"
PORT="${PORT:-8000}"

# Environment mode (dev or prod)
MODE="${1:-prod}"

# Check if .env file exists
if [ ! -f .env ]; then
    echo "❌ .env file not found. Please create one with your configuration."
    echo "Example .env content:"
    echo "OPENAI_API_KEY=your_openai_key"
    echo "DATABASE_URL=your_supabase_connection_string"
    echo "MILVUS_URI=your_milvus_endpoint"
    exit 1
fi

echo "=============================================="
echo "🚀 Starting Agno AI Agent API (${MODE} mode)"
echo "=============================================="

# Stop and remove existing container if running
if docker ps -a --format 'table {{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
    echo "🔄 Stopping existing container..."
    docker stop ${CONTAINER_NAME} >/dev/null 2>&1 || true
    docker rm ${CONTAINER_NAME} >/dev/null 2>&1 || true
fi

# Build the image if it doesn't exist or if in dev mode
if [[ "${MODE}" == "dev" ]] || ! docker image inspect ${IMAGE_NAME}:${IMAGE_TAG} >/dev/null 2>&1; then
    echo "🔨 Building Docker image..."
    docker build -t ${IMAGE_NAME}:${IMAGE_TAG} .
fi

# Run the container
echo "▶️  Starting container on port ${PORT}..."

if [[ "${MODE}" == "dev" ]]; then
    # Development mode with volume mount for live reloading
    docker run -d \
        --name ${CONTAINER_NAME} \
        -p ${PORT}:8000 \
        --env-file .env \
        -e ENVIRONMENT=development \
        -e LOGFIRE_ENVIRONMENT=development \
        -v "$(pwd):/app" \
        --restart unless-stopped \
        --health-cmd "curl -f http://localhost:8000/v1/health || exit 1" \
        --health-interval 30s \
        --health-timeout 10s \
        --health-retries 3 \
        --health-start-period 40s \
        ${IMAGE_NAME}:${IMAGE_TAG}
else
    # Production mode
    docker run -d \
        --name ${CONTAINER_NAME} \
        -p ${PORT}:8000 \
        --env-file .env \
        -e ENVIRONMENT=production \
        -e LOGFIRE_ENVIRONMENT=production \
        --restart unless-stopped \
        --health-cmd "curl -f http://localhost:8000/v1/health || exit 1" \
        --health-interval 30s \
        --health-timeout 10s \
        --health-retries 3 \
        --health-start-period 40s \
        ${IMAGE_NAME}:${IMAGE_TAG}
fi

echo "✅ Container started successfully!"
echo "📍 API URL: http://localhost:${PORT}"
echo "📖 API Docs: http://localhost:${PORT}/docs"
echo ""
echo "📊 Container status:"
docker ps --filter name=${CONTAINER_NAME} --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
echo ""
echo "📝 To view logs: docker logs -f ${CONTAINER_NAME}"
echo "🛑 To stop: docker stop ${CONTAINER_NAME}"