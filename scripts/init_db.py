#!/usr/bin/env python3
"""
Database initialization script for chat system.

This script creates the database tables and runs the initial migration.
Run this before starting the application for the first time.
"""

import subprocess
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from db.session import db_engine, db_url
from db.models import Base
from sqlalchemy import text

def check_database_connection():
    """Check if we can connect to the database."""
    try:
        with db_engine.connect() as conn:
            conn.execute(text("SELECT 1"))
            print("✓ Database connection successful")
            return True
    except Exception as e:
        print(f"✗ Database connection failed: {e}")
        return False

def create_tables():
    """Create database tables using SQLAlchemy metadata."""
    try:
        print("Creating database tables...")
        Base.metadata.create_all(bind=db_engine)
        print("✓ Database tables created successfully")
        return True
    except Exception as e:
        print(f"✗ Failed to create tables: {e}")
        return False

def run_alembic_upgrade():
    """Run Alembic upgrade to apply migrations."""
    try:
        print("Running Alembic migrations...")
        result = subprocess.run(
            ["python", "-m", "alembic", "upgrade", "head"],
            cwd=project_root,
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            print("✓ Alembic migrations completed successfully")
            return True
        else:
            print(f"✗ Alembic migrations failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"✗ Failed to run Alembic migrations: {e}")
        return False

def initialize_database():
    """Initialize the database with tables and initial data."""
    print(f"Initializing database at: {db_url}")
    print("-" * 50)
    
    # Check database connection
    if not check_database_connection():
        print("\nPlease ensure PostgreSQL is running and connection details are correct.")
        return False
    
    # Create tables directly (fallback if Alembic fails)
    if not create_tables():
        return False
    
    # Try to run Alembic migrations (for future schema changes)
    try:
        # Mark current schema as up-to-date with Alembic
        print("Stamping current schema with Alembic...")
        result = subprocess.run(
            ["python", "-m", "alembic", "stamp", "head"],
            cwd=project_root,
            capture_output=True,
            text=True
        )
        if result.returncode == 0:
            print("✓ Alembic stamped successfully")
        else:
            print(f"⚠ Alembic stamp warning: {result.stderr}")
    except Exception as e:
        print(f"⚠ Alembic stamp failed: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 Database initialization completed successfully!")
    print("=" * 50)
    print("\nNext steps:")
    print("1. Start the API server: docker compose up -d")
    print("2. Test the API endpoints at http://localhost:8000/docs")
    print("3. Create chat sessions and messages via the API")
    
    return True

if __name__ == "__main__":
    success = initialize_database()
    sys.exit(0 if success else 1)