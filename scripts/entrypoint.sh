#!/bin/bash

############################################################################
# Container Entrypoint script
############################################################################

# Set Python path to include app directory
export PYTHONPATH=/app:${PYTHONPATH}

# Ensure virtual environment is activated
source /opt/venv/bin/activate
export PATH="/opt/venv/bin:$PATH"

# Check if packages are available
which alembic && which uvicorn || echo "Missing packages"

if [[ "$PRINT_ENV_ON_LOAD" = true || "$PRINT_ENV_ON_LOAD" = True ]]; then
  echo "=================================================="
  printenv
  echo "=================================================="
fi

if [[ "$WAIT_FOR_DB" = true || "$WAIT_FOR_DB" = True ]]; then
  echo "Waiting for database at $DB_HOST:$DB_PORT..."
  dockerize \
    -wait tcp://$DB_HOST:$DB_PORT \
    -timeout 300s
fi

# Run database migrations automatically
if [[ "$RUN_MIGRATIONS" = true || "$RUN_MIGRATIONS" = True ]]; then
  echo "=================================================="
  echo "Running database migrations..."
  echo "=================================================="
  
  # Check if using Supabase (external DB) or local PostgreSQL
  if [[ -n "$DATABASE_URL" ]]; then
    echo "Using Supabase connection..."
    
    # Run Alembic migrations
    alembic upgrade head
    
    if [ $? -eq 0 ]; then
      echo "✅ Migrations completed successfully"
    else
      echo "❌ Migration failed, but continuing (database might already be up to date)"
    fi
  else
    echo "⏭️  Skipping migrations (no DATABASE_URL set)"
  fi
fi

############################################################################
# Start App
############################################################################

case "$1" in
  chill)
    echo "Starting uvicorn server..."
    if [[ "${ENVIRONMENT:-production}" == "development" ]]; then
      echo "Running: uvicorn api.main:app --host 0.0.0.0 --port 8000 --reload"
      exec uvicorn api.main:app --host 0.0.0.0 --port 8000 --reload
    else
      echo "Running: uvicorn api.main:app --host 0.0.0.0 --port 8000 --workers 4"
      exec uvicorn api.main:app --host 0.0.0.0 --port 8000 --workers 4
    fi
    ;;
  *)
    echo "Running: $@"
    exec "$@"
    ;;
esac
