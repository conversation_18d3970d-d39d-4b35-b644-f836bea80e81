#!/usr/bin/env python3
"""
Simple script to view Milvus data for debugging and exploration.
"""
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tools.vector_db.milvus_client_optimized import get_optimized_vector_db

def view_collection_info():
    """View basic collection information."""
    print("🔍 MILVUS DATA VIEWER")
    print("=" * 50)
    
    try:
        # Get vector database instance
        vector_db = get_optimized_vector_db()
        
        # Get performance stats instead
        stats = vector_db.get_performance_stats()
        print(f"📊 Collection: {vector_db.collection_name}")
        print(f"📈 Total Queries: {stats.get('total_queries', 0)}")
        print("=" * 50)
        
        return vector_db
        
    except Exception as e:
        print(f"❌ Error connecting to Milvus: {str(e)}")
        return None

def search_all_documents(vector_db, limit=20):
    """Search for all documents in the collection."""
    try:
        print(f"📋 RECENT DOCUMENTS (showing first {limit})")
        print("-" * 50)
        
        # Create a dummy embedding to search for all documents
        from tools.vector_db.services.embedding_service_optimized import get_optimized_embedding_service
        embedding_service = get_optimized_embedding_service()
        dummy_embedding = embedding_service.generate_embedding("document")
        
        # Search with very broad parameters
        results = vector_db.search_documents(
            query_embedding=dummy_embedding,
            project_id="",  # Empty to search all projects
            limit=limit
        )
        
        if not results:
            print("📭 No documents found in the database.")
            return
        
        print(f"Found {len(results)} documents:")
        print()
        
        for i, doc in enumerate(results, 1):
            print(f"🔖 Document {i}:")
            print(f"   ID: {doc.get('document_id', 'N/A')}")
            print(f"   Name: {doc.get('name', 'N/A')}")
            print(f"   Category: {doc.get('category', 'N/A')}")
            print(f"   Type: {doc.get('document_type', 'N/A')}")
            print(f"   Created: {doc.get('created_at', 'N/A')}")
            print(f"   Project ID: {doc.get('project_id', 'N/A')}")
            print(f"   Similarity Score: {doc.get('similarity_score', 'N/A'):.4f}")
            
            # Show content preview
            content = doc.get('content_text', '')
            if content:
                preview = content[:150] + "..." if len(content) > 150 else content
                print(f"   Content Preview: {preview}")
            
            print("-" * 30)
        
    except Exception as e:
        print(f"❌ Error searching documents: {str(e)}")

def search_by_query(vector_db):
    """Interactive search by query."""
    try:
        print("\n🔍 INTERACTIVE SEARCH")
        print("-" * 30)
        
        query = input("Enter search query (or 'quit' to exit): ").strip()
        if query.lower() == 'quit':
            return
        
        from tools.vector_db.services.embedding_service_optimized import get_optimized_embedding_service
        embedding_service = get_optimized_embedding_service()
        query_embedding = embedding_service.generate_embedding(query)
        
        results = vector_db.search_documents(
            query_embedding=query_embedding,
            project_id="",
            limit=10
        )
        
        if not results:
            print(f"📭 No documents found for query: '{query}'")
            return
        
        print(f"\n📊 Found {len(results)} documents for: '{query}'")
        print("-" * 40)
        
        for i, doc in enumerate(results, 1):
            print(f"🔖 Result {i} (Score: {doc.get('similarity_score', 0):.4f}):")
            print(f"   Name: {doc.get('name', 'N/A')}")
            print(f"   Category: {doc.get('category', 'N/A')}")
            print(f"   Created: {doc.get('created_at', 'N/A')}")
            
            content = doc.get('content_text', '')[:100]
            print(f"   Preview: {content}...")
            print()
        
    except Exception as e:
        print(f"❌ Error in search: {str(e)}")

def main():
    """Main function."""
    vector_db = view_collection_info()
    
    if not vector_db:
        return
    
    # Show all documents
    search_all_documents(vector_db)
    
    # Interactive search
    while True:
        print("\n" + "=" * 50)
        print("OPTIONS:")
        print("1. View all documents again")
        print("2. Search by query")
        print("3. Exit")
        
        choice = input("\nEnter choice (1-3): ").strip()
        
        if choice == '1':
            limit = input("Enter number of documents to show (default 20): ").strip()
            limit = int(limit) if limit.isdigit() else 20
            search_all_documents(vector_db, limit)
        elif choice == '2':
            search_by_query(vector_db)
        elif choice == '3':
            print("👋 Goodbye!")
            break
        else:
            print("❌ Invalid choice. Please enter 1, 2, or 3.")

if __name__ == "__main__":
    main()