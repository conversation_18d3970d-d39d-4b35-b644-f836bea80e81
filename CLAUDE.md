# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an Agno-based Agent API that serves AI agents via FastAPI. The application:
- Uses PostgreSQL with pgvector for agent memories and knowledge storage
- Implements multiple specialized agents (Construction Team, GraphQL Agent)
- Provides RESTful endpoints for agent interactions
- Integrates with Agno Playground for agent testing

## Development Commands

### Environment Setup
```bash
# Install uv if not already installed
curl -LsSf https://astral.sh/uv/install.sh | sh

# Setup virtual environment and install dependencies
./scripts/dev_setup.sh

# Activate virtual environment
source .venv/bin/activate
```

### Code Quality
```bash
# Format code
./scripts/format.sh

# Validate code (lint and type check)
./scripts/validate.sh

# Run linting only
ruff check .

# Run type checking only
mypy . --config-file pyproject.toml
```

### Docker Operations
```bash
# Start API in production mode (requires .env file)
./scripts/run_docker.sh

# Start API in development mode (with live reload)
./scripts/run_docker.sh dev

# Stop API container
./scripts/stop_docker.sh

# Build production image
./scripts/build_image.sh

# Manual docker run (production)
docker run -d --name agent-api -p 8000:8000 --env-file .env -e ENVIRONMENT=production agent-api:latest

# Manual docker run (development with volume mount)
docker run -d --name agent-api -p 8000:8000 --env-file .env -e ENVIRONMENT=development -v "$(pwd):/app" agent-api:latest
```

### Dependency Management
```bash
# After modifying pyproject.toml, regenerate requirements.txt
./scripts/generate_requirements.sh

# Upgrade all dependencies to latest compatible versions
./scripts/generate_requirements.sh upgrade
```

## Architecture

### Core Components

**API Structure** (`/api`)
- `main.py`: FastAPI application factory
- `routes/v1_router.py`: Main v1 API router aggregating all endpoints
- `routes/agents.py`: Agent management endpoints
- `routes/chat.py`: Chat interaction endpoints
- `routes/playground.py`: Agno Playground integration
- `settings.py`: Application configuration

**Agent System** (`/agents`)
- `selector.py`: Agent registry and selection logic
- `construction_team.py`: Construction industry specialized team of agents
- `graphql_agent.py`: GraphQL API interaction specialist

**Tools** (`/tools`)
- `construction/`: Timeline planning, material estimation, safety checklists
- `document/`: Document analysis, extraction, summarization
- `graphql/`: Query execution, schema introspection, query building

**Knowledge Base** (`/knowledge`)
- `graphql/`: GraphQL-specific documentation and patterns
- Loaded into agents via knowledge loader utilities

**Vector Database** (`/tools/vector_db`)
- `services/`: Document processing, embedding generation, vector operations
- `models/`: Data models for vectorization requests/responses
- `utils/`: Document parsing, text chunking utilities
- `milvus_client.py`: Milvus Lite/Standalone integration
- Uses all-MiniLM-L6-v2 model for local embedding generation (384 dimensions)

**Database** (`/db`)
- PostgreSQL with pgvector extension for embeddings
- Handles agent sessions, memories, and knowledge storage

### Agent Architecture

Agents are built using the Agno framework with:
- **Memory**: PostgreSQL-backed persistent memory (PostgresMemoryDb)
- **Storage**: Agent state persistence (PostgresAgentStorage)
- **Tools**: Modular tool functions for specific capabilities
- **Models**: Azure OpenAI models (configurable via model_id)

Team agents (like Construction Team) coordinate multiple specialized sub-agents.

### Key Patterns

1. **Agent Selection**: Use `AgentType` enum in `selector.py` to add new agents
2. **Tool Creation**: Add tool functions in `/tools` with proper type hints
3. **Route Addition**: Include new routers in `v1_router.py`
4. **Knowledge Integration**: Place documentation in `/knowledge` and use knowledge loaders

## Environment Variables

Required for operation:
- `OPENAI_API_KEY`: API key for OpenAI/Azure OpenAI (agents only, not needed for vectorization)
- `DB_*`: PostgreSQL connection parameters (defaults provided)  
- `MILVUS_URI`: Milvus connection URI (./data/milvus_dev.db for development)
- `AGNO_API_KEY`: Optional, for Agno monitoring
- `AGNO_MONITOR`: Set to "True" to enable monitoring

## API Endpoints

- `/docs`: Interactive API documentation
- `/v1/health`: Health check
- `/v1/agents`: Agent management
- `/v1/chat`: Chat interactions
- `/v1/playground`: Agno Playground compatibility
- `/v1/vectorize/document`: Document vectorization for semantic search

## Testing Approach

Check for test scripts in package.json or dedicated test directories. Run validation scripts before committing:
```bash
./scripts/validate.sh
```