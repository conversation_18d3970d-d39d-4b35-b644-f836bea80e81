"""
Agent reasoning tracker for <PERSON>Chain integration.
Captures and structures agent reasoning steps for frontend visualization.
"""
import time
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any, Callable
from contextlib import asynccontextmanager
from dataclasses import dataclass, field

from api.models.thought_chain import (
    ThoughtChainItem,
    AgentThoughtChain,
    ThoughtStatus,
    ThoughtStepType,
    StreamingThoughtUpdate,
    ThoughtChainConfig
)


@dataclass
class ReasoningContext:
    """Context for tracking a single reasoning session."""
    chain_id: str
    session_id: str
    agent_id: str
    user_id: str
    user_message: str
    started_at: datetime = field(default_factory=datetime.now)
    items: List[ThoughtChainItem] = field(default_factory=list)
    current_step: Optional[ThoughtChainItem] = None
    config: ThoughtChainConfig = field(default_factory=ThoughtChainConfig)
    callbacks: List[Callable] = field(default_factory=list)


class ReasoningTracker:
    """
    Tracks agent reasoning steps and formats them for ThoughtChain visualization.
    
    This class provides a clean interface for Agno agents to capture their
    reasoning process without being intrusive to the agent's logic.
    """
    
    def __init__(self, config: Optional[ThoughtChainConfig] = None):
        self.config = config or ThoughtChainConfig()
        self._contexts: Dict[str, ReasoningContext] = {}
        self._callbacks: List[Callable] = []
    
    def start_reasoning_session(
        self,
        session_id: str,
        agent_id: str,
        user_id: str,
        user_message: str,
        chain_id: Optional[str] = None
    ) -> str:
        """
        Start a new reasoning session.
        
        Returns:
            chain_id: Unique identifier for this reasoning chain
        """
        if not self.config.enabled:
            return ""
        
        chain_id = chain_id or str(uuid.uuid4())
        
        context = ReasoningContext(
            chain_id=chain_id,
            session_id=session_id,
            agent_id=agent_id,
            user_id=user_id,
            user_message=user_message,
            config=self.config
        )
        
        self._contexts[chain_id] = context
        
        # Add initial planning step
        self.add_reasoning_step(
            chain_id=chain_id,
            title="Planning Response",
            description="Agent is analyzing the request and planning the response",
            step_type=ThoughtStepType.REASONING,
            status=ThoughtStatus.RUNNING
        )
        
        return chain_id
    
    def add_reasoning_step(
        self,
        chain_id: str,
        title: str,
        description: Optional[str] = None,
        content: Optional[str] = None,
        step_type: ThoughtStepType = ThoughtStepType.REASONING,
        status: ThoughtStatus = ThoughtStatus.PENDING,
        tool_name: Optional[str] = None,
        tool_args: Optional[Dict[str, Any]] = None,
        confidence: Optional[float] = None
    ) -> Optional[int]:
        """Add a new reasoning step to the chain."""
        
        if not self.config.enabled or chain_id not in self._contexts:
            return None
        
        context = self._contexts[chain_id]
        
        # Check chain length limits
        if len(context.items) >= self.config.max_chain_length:
            return None
        
        # Sanitize content if needed
        if content and self.config.sanitize_sensitive_data:
            content = self._sanitize_content(content)
        
        # Truncate content if too long
        if content and len(content) > self.config.max_content_length:
            content = content[:self.config.max_content_length] + "..."
        
        # Create the step
        step = ThoughtChainItem(
            title=title,
            description=description,
            content=content,
            status=status,
            step_type=step_type,
            tool_name=tool_name,
            tool_args=self._sanitize_tool_args(tool_args) if tool_args else None,
            confidence=confidence,
            icon=self._get_step_icon(step_type)
        )
        
        context.items.append(step)
        step_index = len(context.items) - 1
        
        # Notify callbacks about new step
        self._notify_step_update(chain_id, step_index, "new_step")
        
        return step_index
    
    def update_step_status(
        self,
        chain_id: str,
        step_index: int,
        status: ThoughtStatus,
        content: Optional[str] = None,
        tool_result: Optional[str] = None,
        duration_ms: Optional[float] = None,
        error_message: Optional[str] = None
    ) -> bool:
        """Update an existing step's status and details."""
        
        if not self.config.enabled or chain_id not in self._contexts:
            return False
        
        context = self._contexts[chain_id]
        
        if step_index >= len(context.items):
            return False
        
        step = context.items[step_index]
        step.status = status
        
        if content:
            step.content = self._sanitize_content(content) if self.config.sanitize_sensitive_data else content
        
        if tool_result:
            step.tool_result = tool_result
        
        if duration_ms:
            step.duration_ms = duration_ms
        
        if error_message:
            step.error_message = error_message
        
        # Notify callbacks about step update
        self._notify_step_update(chain_id, step_index, "step_update")
        
        return True
    
    @asynccontextmanager
    async def track_tool_execution(
        self,
        chain_id: str,
        tool_name: str,
        tool_args: Dict[str, Any],
        description: Optional[str] = None
    ):
        """
        Context manager for tracking tool execution.
        
        Usage:
            async with tracker.track_tool_execution(chain_id, "search_documents", args) as step_index:
                result = await tool_function(**args)
                tracker.update_step_status(chain_id, step_index, ThoughtStatus.SUCCESS, 
                                          tool_result=str(result))
        """
        start_time = time.time()
        
        # Add tool execution step
        step_index = self.add_reasoning_step(
            chain_id=chain_id,
            title=f"Executing {tool_name}",
            description=description or f"Running tool: {tool_name}",
            step_type=ThoughtStepType.TOOL_CALL,
            status=ThoughtStatus.RUNNING,
            tool_name=tool_name,
            tool_args=tool_args
        )
        
        try:
            yield step_index
        except Exception as e:
            # Update step with error status
            duration = (time.time() - start_time) * 1000
            if step_index is not None:
                self.update_step_status(
                    chain_id=chain_id,
                    step_index=step_index,
                    status=ThoughtStatus.ERROR,
                    error_message=str(e),
                    duration_ms=duration
                )
            raise
        else:
            # Update with success if not already updated
            duration = (time.time() - start_time) * 1000
            if step_index is not None:
                step = self._contexts[chain_id].items[step_index]
                if step.status == ThoughtStatus.RUNNING:
                    self.update_step_status(
                        chain_id=chain_id,
                        step_index=step_index,
                        status=ThoughtStatus.SUCCESS,
                        duration_ms=duration
                    )
    
    def add_memory_access(
        self,
        chain_id: str,
        access_type: str,
        description: str,
        result_summary: Optional[str] = None
    ) -> Optional[int]:
        """Track memory access operations."""
        
        if not self.config.capture_memory_access:
            return None
        
        return self.add_reasoning_step(
            chain_id=chain_id,
            title=f"Memory Access: {access_type}",
            description=description,
            content=result_summary,
            step_type=ThoughtStepType.MEMORY_ACCESS,
            status=ThoughtStatus.SUCCESS
        )
    
    def add_decision_point(
        self,
        chain_id: str,
        decision: str,
        reasoning: str,
        confidence: Optional[float] = None
    ) -> Optional[int]:
        """Track agent decision points."""
        
        return self.add_reasoning_step(
            chain_id=chain_id,
            title=f"Decision: {decision}",
            description=reasoning,
            step_type=ThoughtStepType.DECISION,
            status=ThoughtStatus.SUCCESS,
            confidence=confidence
        )
    
    def complete_reasoning_session(
        self,
        chain_id: str,
        final_response: str,
        success: bool = True
    ) -> Optional[AgentThoughtChain]:
        """Complete a reasoning session and return the final chain."""
        
        if not self.config.enabled or chain_id not in self._contexts:
            return None
        
        context = self._contexts[chain_id]
        
        # Update any pending steps to success
        for step in context.items:
            if step.status in [ThoughtStatus.PENDING, ThoughtStatus.RUNNING]:
                step.status = ThoughtStatus.SUCCESS
        
        # Create the final thought chain
        completed_at = datetime.now()
        total_duration = (completed_at - context.started_at).total_seconds() * 1000
        
        thought_chain = AgentThoughtChain(
            session_id=context.session_id,
            agent_id=context.agent_id,
            user_id=context.user_id,
            chain_id=chain_id,
            started_at=context.started_at,
            completed_at=completed_at,
            total_duration_ms=total_duration,
            items=context.items,
            overall_status=ThoughtStatus.SUCCESS if success else ThoughtStatus.ERROR,
            success=success,
            user_message=context.user_message,
            final_response=final_response,
            total_steps=len(context.items),
            tool_calls=sum(1 for item in context.items if item.step_type == ThoughtStepType.TOOL_CALL),
            memory_accesses=sum(1 for item in context.items if item.step_type == ThoughtStepType.MEMORY_ACCESS),
            errors_encountered=sum(1 for item in context.items if item.status == ThoughtStatus.ERROR)
        )
        
        # Notify completion
        self._notify_chain_complete(chain_id, thought_chain)
        
        # Clean up context
        del self._contexts[chain_id]
        
        return thought_chain
    
    def get_current_chain(self, chain_id: str) -> Optional[AgentThoughtChain]:
        """Get the current state of a reasoning chain."""
        
        if chain_id not in self._contexts:
            return None
        
        context = self._contexts[chain_id]
        
        # Determine overall status
        if any(step.status == ThoughtStatus.ERROR for step in context.items):
            overall_status = ThoughtStatus.ERROR
        elif any(step.status == ThoughtStatus.RUNNING for step in context.items):
            overall_status = ThoughtStatus.RUNNING
        elif any(step.status == ThoughtStatus.PENDING for step in context.items):
            overall_status = ThoughtStatus.PENDING
        else:
            overall_status = ThoughtStatus.SUCCESS
        
        current_time = datetime.now()
        total_duration = (current_time - context.started_at).total_seconds() * 1000
        
        return AgentThoughtChain(
            session_id=context.session_id,
            agent_id=context.agent_id,
            user_id=context.user_id,
            chain_id=chain_id,
            started_at=context.started_at,
            total_duration_ms=total_duration,
            items=context.items.copy(),
            overall_status=overall_status,
            user_message=context.user_message,
            total_steps=len(context.items),
            tool_calls=sum(1 for item in context.items if item.step_type == ThoughtStepType.TOOL_CALL),
            memory_accesses=sum(1 for item in context.items if item.step_type == ThoughtStepType.MEMORY_ACCESS),
            errors_encountered=sum(1 for item in context.items if item.status == ThoughtStatus.ERROR)
        )
    
    def add_callback(self, callback: Callable[[str, StreamingThoughtUpdate], None]):
        """Add a callback for streaming updates."""
        self._callbacks.append(callback)
    
    def _sanitize_content(self, content: str) -> str:
        """Remove sensitive information from content."""
        # Basic sanitization - you can extend this
        sensitive_patterns = [
            "password", "token", "key", "secret", "credential"
        ]
        
        sanitized = content
        for pattern in sensitive_patterns:
            if pattern.lower() in sanitized.lower():
                sanitized = sanitized.replace(pattern, "[REDACTED]")
        
        return sanitized
    
    def _sanitize_tool_args(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Sanitize tool arguments for display."""
        if not self.config.capture_tool_args:
            return {"[HIDDEN]": "Tool arguments hidden for privacy"}
        
        sanitized = {}
        for key, value in args.items():
            if any(sensitive in key.lower() for sensitive in ["password", "token", "key", "secret"]):
                sanitized[key] = "[REDACTED]"
            else:
                # Truncate long values
                str_value = str(value)
                if len(str_value) > 100:
                    sanitized[key] = str_value[:100] + "..."
                else:
                    sanitized[key] = value
        
        return sanitized
    
    def _get_step_icon(self, step_type: ThoughtStepType) -> str:
        """Get appropriate icon for step type."""
        icons = {
            ThoughtStepType.REASONING: "brain",
            ThoughtStepType.TOOL_CALL: "tool",
            ThoughtStepType.MEMORY_ACCESS: "database",
            ThoughtStepType.DECISION: "check-circle",
            ThoughtStepType.VALIDATION: "shield-check",
            ThoughtStepType.ERROR_HANDLING: "alert-triangle"
        }
        return icons.get(step_type, "circle")
    
    def _notify_step_update(self, chain_id: str, step_index: int, update_type: str):
        """Notify callbacks about step updates."""
        if not self._callbacks or chain_id not in self._contexts:
            return
        
        context = self._contexts[chain_id]
        step_data = context.items[step_index] if step_index < len(context.items) else None
        
        update = StreamingThoughtUpdate(
            chain_id=chain_id,
            update_type=update_type,
            step_data=step_data,
            step_index=step_index
        )
        
        for callback in self._callbacks:
            try:
                callback(chain_id, update)
            except Exception as e:
                print(f"Error in thought chain callback: {e}")
    
    def _notify_chain_complete(self, chain_id: str, final_chain: AgentThoughtChain):
        """Notify callbacks about chain completion."""
        if not self._callbacks:
            return
        
        update = StreamingThoughtUpdate(
            chain_id=chain_id,
            update_type="chain_complete",
            final_chain=final_chain
        )
        
        for callback in self._callbacks:
            try:
                callback(chain_id, update)
            except Exception as e:
                print(f"Error in thought chain callback: {e}")


# Global tracker instance
_global_tracker: Optional[ReasoningTracker] = None


def get_reasoning_tracker() -> ReasoningTracker:
    """Get the global reasoning tracker instance."""
    global _global_tracker
    if _global_tracker is None:
        _global_tracker = ReasoningTracker()
    return _global_tracker


def reset_reasoning_tracker():
    """Reset the global tracker (useful for testing)."""
    global _global_tracker
    _global_tracker = None