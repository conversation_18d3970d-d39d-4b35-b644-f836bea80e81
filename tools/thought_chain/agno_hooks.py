"""
Agno framework hooks for ThoughtChain integration.
Seamlessly captures agent reasoning without modifying agent code using pre/post hooks.
"""
import time
import logging
from typing import Any, Dict, Callable, Optional
from contextvars import ContextVar

logger = logging.getLogger(__name__)

try:
    from agno.tools.function_call import FunctionCall
except ImportError:
    # Fallback for older Agno versions or if import path changes
    FunctionCall = Any

from .reasoning_tracker import get_reasoning_tracker
from api.models.thought_chain import ThoughtStatus, ThoughtStepType


# Context variable to track current reasoning chain
current_chain_id: ContextVar[Optional[str]] = ContextVar('current_chain_id', default=None)

# Context variable to track current step for post-hook updates
current_step_data: ContextVar[Optional[Dict[str, Any]]] = ContextVar('current_step_data', default=None)


async def thought_chain_hook(function_name: str, function_call: Callable, arguments: Dict[str, Any]) -> Any:
    """
    Agno hook that captures agent reasoning steps for ThoughtChain visualization.
    
    This hook automatically tracks:
    - Tool executions with timing and results
    - Memory operations
    - Error handling and retries
    
    Usage: Add this hook to your agent's tool_hooks list
    """
    chain_id = current_chain_id.get()
    
    # If no reasoning session is active, just execute normally
    if not chain_id:
        return await function_call(**arguments)
    
    tracker = get_reasoning_tracker()
    
    # Determine step type and description
    step_type = _get_step_type_for_function(function_name)
    description = _generate_step_description(function_name, arguments)
    
    # Use the context manager for tool execution tracking
    async with tracker.track_tool_execution(
        chain_id=chain_id,
        tool_name=function_name,
        tool_args=arguments,
        description=description
    ) as step_index:
        
        start_time = time.time()
        
        try:
            # Execute the actual function
            result = await function_call(**arguments)
            
            # Calculate duration
            duration = (time.time() - start_time) * 1000
            
            # Update step with success and result
            if step_index is not None:
                result_summary = _summarize_result(result)
                tracker.update_step_status(
                    chain_id=chain_id,
                    step_index=step_index,
                    status=ThoughtStatus.SUCCESS,
                    tool_result=result_summary,
                    duration_ms=duration
                )
            
            return result
            
        except Exception:
            # Duration calculated in the context manager's except block
            # Re-raise the exception
            raise


class reasoning_context:
    """
    Context manager for tracking agent reasoning sessions.
    
    Usage:
        async with reasoning_context(session_id, agent_id, user_id, user_message) as chain_id:
            # Agent reasoning happens here
            result = await agent.arun(message)
            # Reasoning is automatically tracked
    """
    
    def __init__(
        self,
        session_id: str,
        agent_id: str,
        user_id: str,
        user_message: str,
        chain_id: Optional[str] = None
    ):
        self.session_id = session_id
        self.agent_id = agent_id
        self.user_id = user_id
        self.user_message = user_message
        self.chain_id = chain_id
        self.tracker = get_reasoning_tracker()
        self.token = None
    
    async def __aenter__(self) -> str:
        """Start the reasoning session."""
        self.chain_id = self.tracker.start_reasoning_session(
            session_id=self.session_id,
            agent_id=self.agent_id,
            user_id=self.user_id,
            user_message=self.user_message,
            chain_id=self.chain_id
        )
        
        # Set the context variable
        self.token = current_chain_id.set(self.chain_id)
        
        return self.chain_id
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Complete the reasoning session."""
        # Reset context variable
        if self.token:
            current_chain_id.reset(self.token)
        
        # Note: The reasoning session is completed by the chat endpoint
        # when it has the final response
        return False  # Don't suppress exceptions


def add_reasoning_step(
    title: str,
    description: Optional[str] = None,
    content: Optional[str] = None,
    step_type: ThoughtStepType = ThoughtStepType.REASONING,
    confidence: Optional[float] = None
) -> Optional[int]:
    """
    Manually add a reasoning step from within agent code.
    
    Usage:
        step_index = add_reasoning_step(
            title="Analyzing user requirements",
            description="Breaking down the user's request into actionable items",
            content="User wants a comprehensive analysis of documents from last week"
        )
    """
    chain_id = current_chain_id.get()
    if not chain_id:
        return None
    
    tracker = get_reasoning_tracker()
    return tracker.add_reasoning_step(
        chain_id=chain_id,
        title=title,
        description=description,
        content=content,
        step_type=step_type,
        status=ThoughtStatus.SUCCESS,
        confidence=confidence
    )


def add_decision_point(
    decision: str,
    reasoning: str,
    confidence: Optional[float] = None
) -> Optional[int]:
    """
    Add a decision point to the reasoning chain.
    
    Usage:
        add_decision_point(
            decision="Use comprehensive analysis approach",
            reasoning="User requested detailed insights and provided specific time period",
            confidence=0.9
        )
    """
    chain_id = current_chain_id.get()
    if not chain_id:
        return None
    
    tracker = get_reasoning_tracker()
    return tracker.add_decision_point(
        chain_id=chain_id,
        decision=decision,
        reasoning=reasoning,
        confidence=confidence
    )


def add_memory_operation(
    access_type: str,
    description: str,
    result_summary: Optional[str] = None
) -> Optional[int]:
    """
    Track memory operations.
    
    Usage:
        add_memory_operation(
            access_type="Chat History Retrieval",
            description="Loading previous conversation context",
            result_summary="Found 5 previous messages in conversation"
        )
    """
    chain_id = current_chain_id.get()
    if not chain_id:
        return None
    
    tracker = get_reasoning_tracker()
    return tracker.add_memory_access(
        chain_id=chain_id,
        access_type=access_type,
        description=description,
        result_summary=result_summary
    )


def _get_step_type_for_function(function_name: str) -> ThoughtStepType:
    """Determine the step type based on function name."""
    
    # Vector/document search operations
    if "search" in function_name.lower() or "vector" in function_name.lower():
        return ThoughtStepType.TOOL_CALL
    
    # Memory operations
    if "memory" in function_name.lower() or "history" in function_name.lower():
        return ThoughtStepType.MEMORY_ACCESS
    
    # Date/time parsing
    if "parse" in function_name.lower() or "date" in function_name.lower():
        return ThoughtStepType.TOOL_CALL
    
    # Analysis operations
    if "analyz" in function_name.lower() or "process" in function_name.lower():
        return ThoughtStepType.TOOL_CALL
    
    # Default to tool call
    return ThoughtStepType.TOOL_CALL


def _generate_step_description(function_name: str, arguments: Dict[str, Any]) -> str:
    """Generate a human-readable description for the step."""
    
    # Handle specific known functions
    if function_name == "search_documents_vector":
        query_text = arguments.get('query_text', 'documents')
        project_id = arguments.get('project_id', 'unknown project')
        start_date = arguments.get('start_date', '')
        end_date = arguments.get('end_date', '')
        
        desc = f"Searching for {query_text} in project {project_id}"
        if start_date and end_date:
            desc += f" from {start_date} to {end_date}"
        elif start_date:
            desc += f" starting from {start_date}"
        return desc
    
    elif function_name == "parse_site_diary_dates":
        query = arguments.get('query', '')
        return f"Parsing dates from query: '{query}'"
    
    elif function_name == "search_similar_documents":
        return "Finding similar documents for context expansion"
    
    elif function_name == "get_vector_performance_stats":
        return "Gathering search performance metrics"
    
    # Generic description
    return f"Executing {function_name.replace('_', ' ')}"


def _summarize_result(result: Any) -> str:
    """Create a concise summary of the function result."""
    
    # Handle None result
    if result is None:
        return "No result returned"
    
    # Handle dict results (common for API responses)
    if isinstance(result, dict):
        if 'total_count' in result:
            count = result.get('total_count', 0)
            success = result.get('success', False)
            return f"Found {count} items (Success: {success})"
        
        if 'success' in result:
            success = result.get('success', False)
            message = result.get('message', '')
            return f"Operation {'succeeded' if success else 'failed'}: {message}"
        
        # Generic dict summary
        return f"Returned dict with {len(result)} keys"
    
    # Handle list results (this could be raw document list when thoughtchain disabled)
    elif isinstance(result, list):
        # Special handling for document lists that might contain dict objects
        if result and isinstance(result[0], dict):
            # Check if it's a list of documents from vector search
            first_item = result[0]
            if any(key in first_item for key in ['document_id', 'name', 'similarity_score']):
                return f"Found {len(result)} documents from vector search"
        return f"Returned list with {len(result)} items"
    
    # Handle string results
    elif isinstance(result, str):
        if len(result) > 100:
            return result[:100] + "..."
        return result
    
    # Handle other types
    else:
        result_str = str(result)
        if len(result_str) > 100:
            return result_str[:100] + "..."
        return result_str


# =============================================================================
# NEW PRE/POST HOOK IMPLEMENTATION FOR REAL-TIME STREAMING
# =============================================================================

def thought_chain_pre_hook(fc: FunctionCall) -> None:
    """
    Agno pre-hook that captures tool execution BEFORE it starts.
    This provides immediate real-time feedback to the thought chain.
    
    Args:
        fc: FunctionCall object containing function name and arguments
    """
    chain_id = current_chain_id.get()
    
    if not chain_id:
        return
    
    tracker = get_reasoning_tracker()
    
    # Extract function details
    function_name = getattr(fc, 'function', {}).get('name', 'unknown_function')
    if hasattr(fc, 'function') and hasattr(fc.function, 'name'):
        function_name = fc.function.name
    elif hasattr(fc, 'name'):
        function_name = fc.name
    
    arguments = getattr(fc, 'arguments', {})
    if not isinstance(arguments, dict):
        arguments = {}
    
    # Generate step details
    step_type = _get_step_type_for_function(function_name)
    description = _generate_step_description(function_name, arguments)
    
    # Create the step with RUNNING status (immediate streaming)
    step_index = tracker.add_reasoning_step(
        chain_id=chain_id,
        title=f"Executing {function_name}",
        description=description,
        step_type=step_type,
        status=ThoughtStatus.RUNNING,
        tool_name=function_name,
        tool_args=arguments
    )
    
    # Store step data for post-hook update
    step_data = {
        'chain_id': chain_id,
        'step_index': step_index,
        'function_name': function_name,
        'start_time': time.time(),
        'arguments': arguments
    }
    current_step_data.set(step_data)
    
    print(f"[ThoughtChain PRE-HOOK] Starting {function_name} - Step {step_index}")


def thought_chain_post_hook(fc: FunctionCall, result: Any = None, error: Optional[Exception] = None) -> None:
    """
    Agno post-hook that captures tool execution results AFTER completion.
    This updates the thought chain with final status and results.
    
    Args:
        fc: FunctionCall object
        result: Result of the function execution
        error: Exception if the function failed
    """
    step_data = current_step_data.get()
    
    if not step_data:
        return
    
    chain_id = step_data['chain_id']
    step_index = step_data['step_index']
    start_time = step_data['start_time']
    
    if step_index is None:
        return
    
    tracker = get_reasoning_tracker()
    
    # Calculate execution duration
    duration_ms = (time.time() - start_time) * 1000
    
    if error:
        # Handle error case
        tracker.update_step_status(
            chain_id=chain_id,
            step_index=step_index,
            status=ThoughtStatus.ERROR,
            error_message=str(error),
            duration_ms=duration_ms
        )
        print(f"[ThoughtChain POST-HOOK] Error in {step_data['function_name']} - {str(error)}")
    else:
        # Handle success case
        result_summary = _summarize_result(result) if result is not None else "Completed successfully"
        tracker.update_step_status(
            chain_id=chain_id,
            step_index=step_index,
            status=ThoughtStatus.SUCCESS,
            tool_result=result_summary,
            duration_ms=duration_ms
        )
        print(f"[ThoughtChain POST-HOOK] Completed {step_data['function_name']} - {result_summary}")
    
    # Clear step data
    current_step_data.set(None)


# Convenience function for agent configuration
def get_thought_chain_hooks():
    """
    Returns pre/post hooks for easy agent configuration.
    
    Usage:
        pre_hook, post_hook = get_thought_chain_hooks()
        agent = Agent(
            ...,
            pre_hooks=[pre_hook],
            post_hooks=[post_hook]
        )
    """
    return thought_chain_pre_hook, thought_chain_post_hook


async def thought_chain_pre_post_hook(function_name: str, function_call: Callable, arguments: Dict[str, Any]) -> Any:
    """
    Combined pre/post hook for Agno tool_hooks that provides real-time thought chain updates.
    This hook executes BEFORE the tool (for immediate streaming) and AFTER (for results).
    
    Args:
        function_name: Name of the function being called
        function_call: The callable function
        arguments: Function arguments
        
    Returns:
        Result of the function execution
    """
    chain_id = current_chain_id.get()
    
    if not chain_id:
        # No reasoning session active, just execute normally
        return await function_call(**arguments)
    
    tracker = get_reasoning_tracker()
    
    # PRE-EXECUTION: Create step with RUNNING status (immediate streaming)
    step_type = _get_step_type_for_function(function_name)
    description = _generate_step_description(function_name, arguments)
    
    step_index = tracker.add_reasoning_step(
        chain_id=chain_id,
        title=f"Executing {function_name}",
        description=description,
        step_type=step_type,
        status=ThoughtStatus.RUNNING,
        tool_name=function_name,
        tool_args=arguments
    )
    
    # Reduced logging for performance - only essential logs in production
    if step_index is not None:
        print(f"[ThoughtChain] Starting {function_name} - Step {step_index}")
    
    # Execute the function and track duration
    start_time = time.time()
    
    try:
        # Execute the actual function
        result = await function_call(**arguments)
        
        # POST-EXECUTION: Update step with success and results
        duration_ms = (time.time() - start_time) * 1000
        
        if step_index is not None and chain_id is not None:
            try:
                result_summary = _summarize_result(result)
                tracker.update_step_status(
                    chain_id=chain_id,
                    step_index=step_index,
                    status=ThoughtStatus.SUCCESS,
                    tool_result=result_summary,
                    duration_ms=duration_ms
                )
                print(f"[ThoughtChain POST] Completed {function_name} - {result_summary}")
            except Exception as summary_error:
                # Fallback if result summarization fails
                logger.warning(f"Failed to summarize result for {function_name}: {summary_error}")
                tracker.update_step_status(
                    chain_id=chain_id,
                    step_index=step_index,
                    status=ThoughtStatus.SUCCESS,
                    tool_result=f"Completed {function_name} (result type: {type(result).__name__})",
                    duration_ms=duration_ms
                )
        
        return result
        
    except Exception as e:
        # POST-EXECUTION: Update step with error
        duration_ms = (time.time() - start_time) * 1000
        
        if step_index is not None and chain_id is not None:
            tracker.update_step_status(
                chain_id=chain_id,
                step_index=step_index,
                status=ThoughtStatus.ERROR,
                error_message=str(e),
                duration_ms=duration_ms
            )
            print(f"[ThoughtChain POST] Error in {function_name} - {str(e)}")
        
        # Re-raise the exception
        raise