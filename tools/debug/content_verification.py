"""
Debug tool to verify content retrieval from <PERSON>lvus and identify hallucination sources.
"""
from typing import Dict, Any, List
from agno.tools import tool
import logging

logger = logging.getLogger(__name__)

@tool(show_result=False)
def verify_document_content(
    project_id: str,
    limit: int = 5
) -> Dict[str, Any]:
    """
    Debug tool to verify what content is actually being retrieved from Milvus.
    
    Args:
        project_id: Project ID to search documents for
        limit: Number of documents to retrieve for verification
    
    Returns:
        Dict with actual document content and metadata for debugging
    """
    try:
        from tools.vector_db.milvus_client_optimized import get_optimized_vector_db
        from tools.vector_db.services.embedding_service_optimized import get_optimized_embedding_service
        
        # Get services
        vector_db = get_optimized_vector_db()
        embedding_service = get_optimized_embedding_service()
        
        # Generate a simple query embedding
        query_embedding = embedding_service.generate_embedding("site diary")
        
        if not query_embedding:
            return {
                'success': False,
                'error': 'Failed to generate embedding',
                'documents': []
            }
        
        # Search for documents
        documents = vector_db.search_documents(
            query_embedding=query_embedding,
            project_id=project_id,
            limit=limit
        )
        
        # Extract key information for debugging
        debug_docs = []
        for i, doc in enumerate(documents[:limit]):
            if isinstance(doc, dict):
                content_text = doc.get('content_text', '')
                
                # Extract date information from document
                name = doc.get('name', '')
                submitted_at = doc.get('submitted_at', '')
                created_at = doc.get('created_at', '')
                
                # Extract date from filename
                from tools.vector_db.milvus_client_optimized import extract_diary_date_from_name
                filename_date = extract_diary_date_from_name(name)
                
                # Look for actual dates in content
                content_dates = []
                if content_text:
                    import re
                    # Look for dates in format DD/MM/YYYY or YYYY-MM-DD
                    date_patterns = [
                        r'\b(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{4})\b',
                        r'\b(\d{4})[\/\-](\d{1,2})[\/\-](\d{1,2})\b',
                        r'(\d{2})\/(\d{2})\/(\d{4})',
                        r'TARIKH\s*:\s*(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{4})',
                        r'(\d{6})',  # YYMMDD format like 250224
                    ]
                    
                    for pattern in date_patterns:
                        matches = re.findall(pattern, content_text)
                        if matches:
                            content_dates.extend(matches)
                
                debug_doc = {
                    'index': i,
                    'name': name,
                    'submitted_at': submitted_at,
                    'created_at': created_at,
                    'filename_extracted_date': filename_date.strftime('%Y-%m-%d') if filename_date else None,
                    'content_text_length': len(content_text),
                    'content_text_preview': content_text[:300] + '...' if len(content_text) > 300 else content_text,
                    'content_dates_found': content_dates[:5],  # First 5 dates found
                    'has_content_text': bool(content_text),
                    'document_id': doc.get('document_id', ''),
                    'category': doc.get('category', ''),
                    'workspace_group': doc.get('workspace_group', '')
                }
                
                debug_docs.append(debug_doc)
        
        return {
            'success': True,
            'total_documents': len(documents),
            'debug_documents': debug_docs,
            'project_id': project_id,
            'timestamp': str(logger.info("Content verification completed"))
        }
        
    except Exception as e:
        logger.error(f"❌ Content verification failed: {e}")
        return {
            'success': False,
            'error': str(e),
            'documents': []
        }