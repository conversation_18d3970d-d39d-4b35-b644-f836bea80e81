"""
Database audit tool to investigate document count inconsistencies.
"""
from typing import Dict, Any
from agno.tools.function import Function
import logging

logger = logging.getLogger(__name__)


def _audit_database_documents_impl(
    project_id: str,
    document_type: str = "site_diary",
    date_range_days: int = 30
) -> Dict[str, Any]:
    """
    Audit database documents to investigate inconsistencies.
    
    Args:
        project_id: Project ID to audit
        document_type: Type of documents to audit (default: site_diary)  
        date_range_days: Number of days to look back (default: 30)
    
    Returns:
        Detailed audit report showing document counts and dates
    """
    try:
        from tools.vector_db.milvus_client_optimized import get_optimized_vector_db, extract_diary_date_from_name
        from tools.vector_db.services.embedding_service_optimized import get_optimized_embedding_service
        from datetime import datetime, timedelta
        
        # Get clients
        vector_db = get_optimized_vector_db()
        embedding_service = get_optimized_embedding_service()
        
        # Create a broad search query
        dummy_embedding = embedding_service.generate_embedding("site diary document")
        
        # Search for all documents in project (high limit to get everything)
        results = vector_db.search_similar(
            query_embedding=dummy_embedding,
            project_id=project_id,
            limit=1000,  # High limit to get all documents
            filters={}   # No date filters to get everything
        )
        
        audit_results = {
            "project_id": project_id,
            "total_documents_found": len(results),
            "audit_timestamp": datetime.now().isoformat(),
            "documents_by_type": {},
            "date_analysis": {},
            "problematic_documents": [],
            "document_details": []
        }
        
        # Analyze each document
        site_diary_docs = []
        date_extraction_failures = []
        date_range_start = datetime.now() - timedelta(days=date_range_days)
        recent_docs = []
        
        for result in results:
            doc_name = result.get("document_name", "") or result.get("name", "")
            doc_category = result.get("category", "")
            submitted_at = result.get("submitted_at", "")
            created_at = result.get("created_at", "")
            
            # Extract date from filename
            extracted_date = extract_diary_date_from_name(doc_name)
            
            doc_detail = {
                "name": doc_name,
                "category": doc_category,
                "extracted_date": extracted_date.strftime('%Y-%m-%d') if extracted_date else None,
                "submitted_at": submitted_at,
                "created_at": created_at,
                "has_content": bool(result.get("content_text", ""))
            }
            
            audit_results["document_details"].append(doc_detail)
            
            # Categorize documents
            if "site diary" in doc_name.lower() or doc_category.lower() == "site diary":
                site_diary_docs.append(doc_detail)
                
                # Check for date extraction issues
                if not extracted_date:
                    date_extraction_failures.append(doc_name)
                elif extracted_date >= date_range_start.date():
                    recent_docs.append(doc_detail)
        
        # Generate summary statistics
        audit_results["documents_by_type"] = {
            "site_diary": len(site_diary_docs),
            "other": len(results) - len(site_diary_docs)
        }
        
        audit_results["date_analysis"] = {
            f"recent_{date_range_days}_days": len(recent_docs),
            "date_extraction_failures": len(date_extraction_failures),
            "failure_documents": date_extraction_failures
        }
        
        # Find documents in the expected "last week" range
        last_week_start = datetime.now() - timedelta(days=14)  # Same as date parser
        last_week_docs = [
            doc for doc in recent_docs 
            if doc["extracted_date"] and 
               datetime.strptime(doc["extracted_date"], '%Y-%m-%d').date() >= last_week_start.date()
        ]
        
        audit_results["last_week_analysis"] = {
            "expected_range": f"{last_week_start.strftime('%Y-%m-%d')} to {datetime.now().strftime('%Y-%m-%d')}",
            "documents_in_range": len(last_week_docs),
            "document_names": [doc["name"] for doc in last_week_docs],
            "document_dates": [doc["extracted_date"] for doc in last_week_docs]
        }
        
        logger.info(f"📊 Database audit complete:")
        logger.info(f"   - Total documents: {audit_results['total_documents_found']}")
        logger.info(f"   - Site diaries: {len(site_diary_docs)}")
        logger.info(f"   - Recent ({date_range_days} days): {len(recent_docs)}")
        logger.info(f"   - Last week range: {len(last_week_docs)}")
        
        return {
            "success": True,
            "audit_results": audit_results
        }
        
    except Exception as e:
        logger.error(f"❌ Database audit failed: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "audit_results": {}
        }


# Create the Function tool
audit_database_documents = Function(
    name="audit_database_documents",
    description="""
    Audit database documents to investigate count and date inconsistencies.
    
    This tool provides a comprehensive view of what documents actually exist
    in the database, their dates, and helps identify why search results might
    be inconsistent.
    
    Use this tool to:
    - Check how many site diary documents actually exist
    - Verify document dates and metadata
    - Identify missing or problematic documents
    - Debug date filtering issues
    """,
    parameters={
        "type": "object",
        "properties": {
            "project_id": {
                "type": "string",
                "description": "Project ID to audit"
            },
            "document_type": {
                "type": "string", 
                "default": "site_diary",
                "description": "Type of documents to focus on (default: site_diary)"
            },
            "date_range_days": {
                "type": "integer",
                "default": 30,
                "description": "Number of days to look back for recent documents (default: 30)"
            }
        }
    },
    required=["project_id"],
    function_call=_audit_database_documents_impl
)