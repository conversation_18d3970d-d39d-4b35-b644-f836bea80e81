import os
import requests
from agno.tools import tool
from typing import Dict, Any, Optional


@tool
def execute_graphql(
    query: str, 
    variables: Optional[Dict[str, Any]] = None, 
    endpoint: Optional[str] = None, 
    auth_token: Optional[str] = None, 
    project_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Execute GraphQL queries or mutations against any GraphQL API
    
    Args:
        query: GraphQL query or mutation string
        variables: Optional variables for the query
        endpoint: GraphQL endpoint URL (uses default if not provided)
        auth_token: Bearer token for authentication (overrides environment variables)
        project_id: Project ID header for API requests
    
    Returns:
        GraphQL response with data and any errors
    """
    # Use default endpoint if none provided
    if not endpoint:
        endpoint = os.getenv('GRAPHQL_ENDPOINT', 'http://localhost:3000/graphql')
    
    # Note: auth_token and project_id will be passed explicitly by the agent
    # when calling this tool, so no need to extract from agent context
    
    # Prepare headers
    headers = {
        'Content-Type': 'application/json',
    }
    
    # Add authorization - prioritize passed token, then agent state, then environment variables
    token = auth_token or os.getenv('GRAPHQL_AUTH_TOKEN') or os.getenv('GITHUB_TOKEN')
    if token and token.strip():  # Only add if token exists and is not empty
        # Handle token that may already include 'Bearer ' prefix
        if token.startswith('Bearer '):
            headers['Authorization'] = token
        else:
            headers['Authorization'] = f'Bearer {token}'
    
    # Add project-id header if provided
    if project_id and str(project_id).strip():
        headers['project-id'] = str(project_id)
    
    # Prepare payload
    payload = {
        'query': query,
        'variables': variables or {}
    }
    
    try:
        print(f"DEBUG: Making GraphQL request to {endpoint}")
        print(f"DEBUG: Headers: {headers}")
        print(f"DEBUG: Payload: {payload}")
        
        response = requests.post(endpoint, json=payload, headers=headers, timeout=30)
        response.raise_for_status()
        
        result = response.json()
        print(f"DEBUG: Response: {result}")
        
        # Return clean response
        return {
            'success': True,
            'data': result.get('data'),
            'errors': result.get('errors'),
            'endpoint': endpoint
        }
        
    except requests.exceptions.RequestException as e:
        error_msg = f'Request failed: {str(e)}'
        print(f"DEBUG: Request error: {error_msg}")
        return {
            'success': False,
            'error': error_msg,
            'data': None,
            'errors': None
        }
    except Exception as e:
        error_msg = f'Unexpected error: {str(e)}'
        print(f"DEBUG: Unexpected error: {error_msg}")
        return {
            'success': False,
            'error': error_msg,
            'data': None,
            'errors': None
        }
