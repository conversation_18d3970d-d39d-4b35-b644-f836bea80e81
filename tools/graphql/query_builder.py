from agno.tools import tool
from typing import Dict, List, Any


@tool
def build_graphql_query(
    operation_type: str,
    fields: List[str],
    operation_name: str = None,
    variables: Dict[str, str] = None,
    arguments: Dict[str, Any] = None
) -> str:
    """
    Build a GraphQL query or mutation from components
    
    Args:
        operation_type: 'query' or 'mutation'
        fields: List of fields to request
        operation_name: Optional name for the operation
        variables: Optional variable definitions (name -> type)
        arguments: Optional arguments for the root field
    
    Returns:
        Formatted GraphQL query string
    """
    try:
        query_parts = []
        
        # Build operation line
        operation_line = operation_type
        if operation_name:
            operation_line += f" {operation_name}"
        
        # Add variables if provided
        if variables:
            var_defs = []
            for var_name, var_type in variables.items():
                var_defs.append(f"${var_name}: {var_type}")
            operation_line += f"({', '.join(var_defs)})"
        
        query_parts.append(operation_line + " {")
        
        # Build field selection
        if arguments:
            arg_strings = []
            for arg_name, arg_value in arguments.items():
                if isinstance(arg_value, str) and not arg_value.startswith('$'):
                    arg_strings.append(f'{arg_name}: "{arg_value}"')
                else:
                    arg_strings.append(f'{arg_name}: {arg_value}')
            
            # Assume first field is the root field that takes arguments
            if fields:
                root_field = fields[0]
                remaining_fields = fields[1:] if len(fields) > 1 else []
                
                if remaining_fields:
                    query_parts.append(f"  {root_field}({', '.join(arg_strings)}) {{")
                    for field in remaining_fields:
                        query_parts.append(f"    {field}")
                    query_parts.append("  }")
                else:
                    query_parts.append(f"  {root_field}({', '.join(arg_strings)})")
            else:
                query_parts.append(f"  root({', '.join(arg_strings)})")
        else:
            # Simple field selection
            for field in fields:
                query_parts.append(f"  {field}")
        
        query_parts.append("}")
        
        return "\n".join(query_parts)
        
    except Exception as e:
        return f"Error building query: {str(e)}"


@tool
def validate_graphql_syntax(query: str) -> Dict[str, Any]:
    """
    Basic validation of GraphQL query syntax
    
    Args:
        query: GraphQL query string to validate
    
    Returns:
        Validation result with success status and any issues found
    """
    issues = []
    query = query.strip()
    
    # Basic syntax checks
    if not query:
        issues.append("Query is empty")
        return {'valid': False, 'issues': issues}
    
    # Check for balanced braces
    open_braces = query.count('{')
    close_braces = query.count('}')
    if open_braces != close_braces:
        issues.append(f"Unbalanced braces: {open_braces} opening, {close_braces} closing")
    
    # Check for balanced parentheses
    open_parens = query.count('(')
    close_parens = query.count(')')
    if open_parens != close_parens:
        issues.append(f"Unbalanced parentheses: {open_parens} opening, {close_parens} closing")
    
    # Check if it starts with valid operation
    first_word = query.split()[0].lower() if query.split() else ""
    if first_word not in ['query', 'mutation', 'subscription', '{']:
        if not query.startswith('{'):
            issues.append("Query should start with 'query', 'mutation', 'subscription', or '{'")
    
    # Check for common syntax issues
    if '""' in query and '\\""' not in query:
        issues.append("Empty string literals found - consider if this is intentional")
    
    # Check for unclosed strings
    quote_count = query.count('"') - query.count('\\"')
    if quote_count % 2 != 0:
        issues.append("Unclosed string literal detected")
    
    return {
        'valid': len(issues) == 0,
        'issues': issues,
        'query': query
    }