"""
Tools for searching documents by submitted_at field in Milvus.
"""

from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)


def search_by_submitted_at(
    query_text: str,
    project_id: str,
    submitted_start: str,
    submitted_end: str,
    limit: int = 10
) -> Dict[str, Any]:
    """
    Search documents by submitted_at date range.
    
    Args:
        query_text: Text query for semantic search
        project_id: Project identifier
        submitted_start: Start date in ISO format (e.g., "2025-09-10T00:00:00")
        submitted_end: End date in ISO format (e.g., "2025-09-10T23:59:59")
        limit: Maximum number of results
        
    Returns:
        Dictionary with search results
    """
    try:
        from tools.vector_db.milvus_client_optimized import get_optimized_vector_db
        from tools.vector_db.services.embedding_service_optimized import get_optimized_embedding_service
        
        # Get services
        vector_db = get_optimized_vector_db()
        embedding_service = get_optimized_embedding_service()
        
        # Generate embedding
        query_embedding = embedding_service.generate_embedding(query_text)
        
        # Search with submitted_at filters
        results = vector_db.search_similar(
            embedding=query_embedding,
            project_id=project_id,
            limit=limit,
            filters={
                'submitted_start': submitted_start,
                'submitted_end': submitted_end
            }
        )
        
        if isinstance(results, tuple):
            documents, query_time = results
        else:
            documents = results
            query_time = 0
        
        return {
            'success': True,
            'total_count': len(documents),
            'documents': documents,
            'query_time': query_time,
            'filter_type': 'submitted_at',
            'date_range': f"{submitted_start} to {submitted_end}"
        }
        
    except Exception as e:
        logger.error(f"Error in submitted_at search: {e}")
        return {
            'success': False,
            'error': str(e),
            'total_count': 0,
            'documents': []
        }


def search_by_submitted_date(
    query_text: str,
    project_id: str,
    target_date: str,
    limit: int = 10
) -> Dict[str, Any]:
    """
    Search documents submitted on a specific date.
    
    Args:
        query_text: Text query for semantic search
        project_id: Project identifier
        target_date: Target date in YYYY-MM-DD format (e.g., "2025-09-10")
        limit: Maximum number of results
        
    Returns:
        Dictionary with search results
    """
    # Convert date to full day range
    start_time = f"{target_date}T00:00:00"
    end_time = f"{target_date}T23:59:59"
    
    return search_by_submitted_at(
        query_text=query_text,
        project_id=project_id,
        submitted_start=start_time,
        submitted_end=end_time,
        limit=limit
    )


def search_recent_submissions(
    query_text: str,
    project_id: str,
    days_back: int = 7,
    limit: int = 10
) -> Dict[str, Any]:
    """
    Search documents submitted in the last N days.
    
    Args:
        query_text: Text query for semantic search
        project_id: Project identifier
        days_back: Number of days to look back (default: 7)
        limit: Maximum number of results
        
    Returns:
        Dictionary with search results
    """
    # Calculate date range
    end_date = datetime.now()
    start_date = end_date - timedelta(days=days_back)
    
    start_time = start_date.isoformat()
    end_time = end_date.isoformat()
    
    return search_by_submitted_at(
        query_text=query_text,
        project_id=project_id,
        submitted_start=start_time,
        submitted_end=end_time,
        limit=limit
    )


def search_with_custom_filter(
    query_text: str,
    project_id: str,
    custom_filter: str,
    limit: int = 10
) -> Dict[str, Any]:
    """
    Search documents with a custom Milvus filter expression.
    
    Args:
        query_text: Text query for semantic search
        project_id: Project identifier
        custom_filter: Custom Milvus filter expression
        limit: Maximum number of results
        
    Returns:
        Dictionary with search results
        
    Examples:
        # Search by exact submission time
        custom_filter = 'submitted_at >= "2025-09-10T01:00:00" and submitted_at <= "2025-09-10T02:00:00"'
        
        # Search by document type and submission date
        custom_filter = 'category == "site_diary" and submitted_at >= "2025-09-10T00:00:00"'
    """
    try:
        from tools.vector_db.milvus_client_optimized import get_optimized_vector_db
        from tools.vector_db.services.embedding_service_optimized import get_optimized_embedding_service
        
        # Get services
        vector_db = get_optimized_vector_db()
        embedding_service = get_optimized_embedding_service()
        
        # Generate embedding
        query_embedding = embedding_service.generate_embedding(query_text)
        
        # Search with custom filter
        results = vector_db.search_similar(
            embedding=query_embedding,
            project_id=project_id,
            limit=limit,
            filters={'custom_filter': custom_filter}
        )
        
        if isinstance(results, tuple):
            documents, query_time = results
        else:
            documents = results
            query_time = 0
        
        return {
            'success': True,
            'total_count': len(documents),
            'documents': documents,
            'query_time': query_time,
            'filter_type': 'custom',
            'custom_filter': custom_filter
        }
        
    except Exception as e:
        logger.error(f"Error in custom filter search: {e}")
        return {
            'success': False,
            'error': str(e),
            'total_count': 0,
            'documents': []
        }


def get_submission_date_stats(project_id: str) -> Dict[str, Any]:
    """
    Get statistics about document submission dates in a project.
    
    Args:
        project_id: Project identifier
        
    Returns:
        Dictionary with submission date statistics
    """
    try:
        from tools.vector_db.milvus_client_optimized import get_optimized_vector_db
        from tools.vector_db.services.embedding_service_optimized import get_optimized_embedding_service
        
        # Get services
        vector_db = get_optimized_vector_db()
        embedding_service = get_optimized_embedding_service()
        
        # Create dummy embedding to get all documents
        dummy_embedding = embedding_service.generate_embedding("document")
        
        # Get all documents in project
        results = vector_db.search_similar(
            embedding=dummy_embedding,
            project_id=project_id,
            limit=1000,  # High limit to get all documents
            filters={}
        )
        
        if isinstance(results, tuple):
            documents, _ = results
        else:
            documents = results
        
        # Analyze submission dates
        submission_dates = []
        invalid_dates = 0
        
        for doc in documents:
            submitted_at = doc.get('submitted_at', '')
            if submitted_at:
                try:
                    # Parse the submission date
                    submission_date = datetime.fromisoformat(submitted_at.replace('Z', '+00:00'))
                    submission_dates.append(submission_date)
                except:
                    invalid_dates += 1
        
        if not submission_dates:
            return {
                'total_documents': len(documents),
                'valid_submission_dates': 0,
                'invalid_dates': invalid_dates,
                'date_range': None
            }
        
        # Calculate statistics
        earliest_date = min(submission_dates)
        latest_date = max(submission_dates)
        
        # Group by date
        date_counts = {}
        for date in submission_dates:
            date_str = date.strftime('%Y-%m-%d')
            date_counts[date_str] = date_counts.get(date_str, 0) + 1
        
        return {
            'total_documents': len(documents),
            'valid_submission_dates': len(submission_dates),
            'invalid_dates': invalid_dates,
            'earliest_submission': earliest_date.isoformat(),
            'latest_submission': latest_date.isoformat(),
            'date_range_days': (latest_date - earliest_date).days,
            'submissions_by_date': date_counts,
            'most_active_date': max(date_counts.items(), key=lambda x: x[1]) if date_counts else None
        }
        
    except Exception as e:
        logger.error(f"Error getting submission date stats: {e}")
        return {
            'error': str(e),
            'total_documents': 0
        }


# Example usage functions
def example_queries():
    """Show example queries using submitted_at field."""
    
    examples = {
        "Search documents submitted today": {
            "function": "search_by_submitted_date",
            "params": {
                "query_text": "site diary",
                "project_id": "51",
                "target_date": "2025-09-10"
            }
        },
        
        "Search documents submitted in last 7 days": {
            "function": "search_recent_submissions", 
            "params": {
                "query_text": "site diary",
                "project_id": "51",
                "days_back": 7
            }
        },
        
        "Search with custom time range": {
            "function": "search_by_submitted_at",
            "params": {
                "query_text": "site diary",
                "project_id": "51", 
                "submitted_start": "2025-09-10T01:00:00",
                "submitted_end": "2025-09-10T02:00:00"
            }
        },
        
        "Search with complex filter": {
            "function": "search_with_custom_filter",
            "params": {
                "query_text": "site diary",
                "project_id": "51",
                "custom_filter": 'category == "site_diary" and submitted_at >= "2025-09-10T00:00:00"'
            }
        }
    }
    
    return examples
