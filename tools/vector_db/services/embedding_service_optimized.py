"""
Ultra-optimized embedding generation service with advanced caching, batching, and preloading.
"""
import hashlib
import time
import threading
import pickle
import os
from typing import List, Dict, Optional, Any
from functools import lru_cache
from concurrent.futures import ThreadPoolExecutor
from collections import OrderedDict
from sentence_transformers import SentenceTransformer
import torch
import logging

logger = logging.getLogger(__name__)


class OptimizedEmbeddingService:
    """Ultra-optimized embedding service with multiple performance enhancements."""
    
    _instance = None
    _model = None
    _lock = threading.Lock()
    
    def __new__(cls, model: str = "all-MiniLM-L6-v2"):
        """Thread-safe singleton pattern."""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance.model_name = model
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self, model: str = "all-MiniLM-L6-v2"):
        """Initialize with performance optimizations."""
        if self._initialized:
            return
            
        with self._lock:
            if self._initialized:
                return
                
            self.model_name = model
            self.max_tokens = 256
            self.batch_size = 32  # Process embeddings in batches
            
            # Advanced caching with OrderedDict for LRU behavior
            self._embedding_cache = OrderedDict()
            self._cache_max_size = 5000  # Increased cache size
            self._cache_hits = 0
            self._cache_misses = 0
            
            # Persistent cache file
            self._cache_file = f"/tmp/embeddings_cache_{model.replace('/', '_')}.pkl"
            
            # Thread pool for parallel processing
            self._executor = ThreadPoolExecutor(max_workers=4)
            
            # Preload model and warmup
            self._load_model()
            self._load_persistent_cache()
            self._warmup_model()
            
            self._initialized = True
            
            logger.info(f"✅ OptimizedEmbeddingService initialized with {model}")
            logger.info(f"   - Batch size: {self.batch_size}")
            logger.info(f"   - Cache size: {self._cache_max_size}")
            logger.info(f"   - Device: {self._get_device()}")
    
    @property
    def model(self):
        """Compatibility property for accessing model name."""
        return self.model_name
    
    def _get_device(self):
        """Get optimal device for computation."""
        if torch.cuda.is_available():
            return 'cuda'
        elif torch.backends.mps.is_available():  # Apple Silicon
            return 'mps'
        else:
            return 'cpu'
    
    def _load_model(self):
        """Load model with optimizations."""
        if self._model is None:
            logger.info(f"🔄 Loading optimized model: {self.model_name}")
            start_time = time.time()
            
            # Load with optimal device
            device = self._get_device()
            self._model = SentenceTransformer(self.model_name, device=device)
            
            # Enable eval mode for inference
            self._model.eval()
            
            # Set max sequence length for consistent performance
            self._model.max_seq_length = self.max_tokens
            
            load_time = time.time() - start_time
            logger.info(f"✅ Model loaded on {device} in {load_time:.2f}s")
        
        return self._model
    
    def _warmup_model(self):
        """Warmup model with dummy data to optimize initial inference."""
        logger.info("🔥 Warming up model...")
        dummy_texts = ["warmup text"] * 5
        try:
            with torch.no_grad():
                _ = self._model.encode(
                    dummy_texts,
                    batch_size=self.batch_size,
                    show_progress_bar=False,
                    convert_to_numpy=True,
                    normalize_embeddings=True
                )
            logger.info("✅ Model warmup complete")
        except Exception as e:
            logger.warning(f"⚠️ Model warmup failed: {e}")
    
    def _load_persistent_cache(self):
        """Load cache from disk if available."""
        if os.path.exists(self._cache_file):
            try:
                with open(self._cache_file, 'rb') as f:
                    cache_data = pickle.load(f)
                    if isinstance(cache_data, dict):
                        # Convert to OrderedDict and limit size
                        items = list(cache_data.items())[-self._cache_max_size:]
                        self._embedding_cache = OrderedDict(items)
                        logger.info(f"📂 Loaded {len(self._embedding_cache)} cached embeddings from disk")
            except Exception as e:
                logger.warning(f"⚠️ Could not load persistent cache: {e}")
    
    def _save_persistent_cache(self):
        """Save cache to disk for persistence."""
        try:
            # Save only the most recent entries
            cache_data = dict(list(self._embedding_cache.items())[-1000:])
            with open(self._cache_file, 'wb') as f:
                pickle.dump(cache_data, f)
        except Exception as e:
            logger.warning(f"⚠️ Could not save persistent cache: {e}")
    
    def _get_cache_key(self, text: str) -> str:
        """Generate cache key with improved hashing."""
        # Include model name in cache key for multi-model support
        key_string = f"{self.model_name}:{text}"
        return hashlib.sha256(key_string.encode('utf-8')).hexdigest()[:32]
    
    def _update_cache_stats(self, hit: bool):
        """Update cache statistics."""
        if hit:
            self._cache_hits += 1
        else:
            self._cache_misses += 1
        
        # Log stats every 100 requests
        total = self._cache_hits + self._cache_misses
        if total % 100 == 0:
            hit_rate = (self._cache_hits / total) * 100 if total > 0 else 0
            logger.info(f"📊 Cache stats: {hit_rate:.1f}% hit rate ({self._cache_hits}/{total})")
    
    def _manage_cache_size(self):
        """Efficient cache size management using OrderedDict."""
        if len(self._embedding_cache) > self._cache_max_size:
            # Remove oldest items (FIFO with OrderedDict)
            excess = len(self._embedding_cache) - int(self._cache_max_size * 0.9)
            for _ in range(excess):
                self._embedding_cache.popitem(last=False)
    
    @lru_cache(maxsize=500)
    def _preprocess_text(self, text: str) -> str:
        """Optimized text preprocessing with larger LRU cache."""
        if not text or not isinstance(text, str):
            return ""
        
        text = text.strip()
        
        # Optimized truncation
        if len(text) > 1000:
            # Smart truncation - try to cut at sentence boundary
            text = text[:1000]
            last_period = text.rfind('.')
            if last_period > 800:
                text = text[:last_period + 1]
        
        return text
    
    def generate_embedding(self, text: str) -> Optional[List[float]]:
        """Generate embedding with caching and optimization."""
        if not text:
            return None
        
        # Preprocess text
        processed_text = self._preprocess_text(text)
        if not processed_text:
            return None
        
        # Check cache
        cache_key = self._get_cache_key(processed_text)
        if cache_key in self._embedding_cache:
            # Move to end (most recently used)
            self._embedding_cache.move_to_end(cache_key)
            self._update_cache_stats(hit=True)
            return self._embedding_cache[cache_key]
        
        # Final validation before encoding
        if not isinstance(processed_text, str) or not processed_text.strip():
            logger.warning(f"⚠️ Invalid processed text for embedding: {type(processed_text)} - {repr(processed_text)}")
            return None
        
        # Generate embedding
        try:
            # Ensure model is loaded
            if self._model is None:
                logger.info("🔄 Model not loaded, loading now...")
                self._load_model()
                
            if self._model is None:
                logger.error("❌ Failed to load embedding model")
                return None
                
            with torch.no_grad():
                embedding = self._model.encode(
                    [processed_text],
                    batch_size=1,
                    show_progress_bar=False,
                    convert_to_numpy=True,
                    normalize_embeddings=True
                )[0].tolist()
            
            # Update cache
            self._embedding_cache[cache_key] = embedding
            self._manage_cache_size()
            self._update_cache_stats(hit=False)
            
            return embedding
            
        except Exception as e:
            logger.error(f"❌ Embedding generation failed for text '{processed_text}': {e}")
            return None
    
    def generate_embeddings_batch(self, texts: List[str]) -> List[Optional[List[float]]]:
        """Generate embeddings for multiple texts with batching."""
        if not texts:
            return []
        
        results = [None] * len(texts)
        texts_to_encode = []
        indices_to_encode = []
        
        # Check cache and prepare texts that need encoding
        for i, text in enumerate(texts):
            if not text:
                continue
                
            processed_text = self._preprocess_text(text)
            if not processed_text:
                continue
            
            cache_key = self._get_cache_key(processed_text)
            if cache_key in self._embedding_cache:
                # Use cached embedding
                self._embedding_cache.move_to_end(cache_key)
                results[i] = self._embedding_cache[cache_key]
                self._update_cache_stats(hit=True)
            else:
                # Need to generate
                texts_to_encode.append(processed_text)
                indices_to_encode.append(i)
                self._update_cache_stats(hit=False)
        
        # Batch encode texts that aren't cached
        if texts_to_encode:
            # Validate all texts are non-empty strings before encoding
            valid_texts = []
            valid_indices = []
            
            for i, text in enumerate(texts_to_encode):
                if text and isinstance(text, str) and text.strip():
                    valid_texts.append(text)
                    valid_indices.append(indices_to_encode[i])
                else:
                    logger.warning(f"⚠️ Skipping invalid text at batch index {i}: {type(text)} - {repr(text)}")
            
            if valid_texts:
                try:
                    # Ensure model is loaded
                    if self._model is None:
                        logger.info("🔄 Model not loaded for batch processing, loading now...")
                        self._load_model()
                        
                    if self._model is None:
                        logger.error("❌ Failed to load embedding model for batch processing")
                        return results
                        
                    logger.info(f"🔄 Batch encoding {len(valid_texts)} valid texts...")
                    start_time = time.time()
                    
                    with torch.no_grad():
                        embeddings = self._model.encode(
                            valid_texts,
                            batch_size=min(self.batch_size, len(valid_texts)),
                            show_progress_bar=False,
                            convert_to_numpy=True,
                            normalize_embeddings=True
                        )
                    
                    # Store results and update cache using valid indices
                    for idx, text, embedding in zip(valid_indices, valid_texts, embeddings):
                        embedding_list = embedding.tolist()
                        results[idx] = embedding_list
                        
                        cache_key = self._get_cache_key(text)
                        self._embedding_cache[cache_key] = embedding_list
                    
                    self._manage_cache_size()
                    
                    encoding_time = time.time() - start_time
                    logger.info(f"✅ Batch encoding completed in {encoding_time:.2f}s "
                              f"({len(valid_texts)/encoding_time:.1f} texts/sec)")
                    
                except Exception as e:
                    logger.error(f"❌ Batch embedding generation failed: {e}")
                    logger.error(f"❌ Failed on texts: {[type(t) for t in valid_texts[:3]]}")
            else:
                logger.warning("⚠️ No valid texts to encode in batch")
        
        return results
    
    def embed_document_chunks(self, chunks) -> tuple[List[List[float]], int]:
        """
        Embed document chunks and return embeddings with total token count.
        
        Args:
            chunks: List of DocumentChunk objects or text strings to embed
            
        Returns:
            Tuple of (embeddings_list, total_token_count)
        """
        logger.info(f"🔢 Embedding {len(chunks)} document chunks...")
        
        # Extract text content from chunks (handle both DocumentChunk objects and strings)
        text_chunks = []
        for chunk in chunks:
            if hasattr(chunk, 'content'):  # DocumentChunk object
                text_chunks.append(chunk.content)
            elif isinstance(chunk, str):   # Plain string
                text_chunks.append(chunk)
            else:
                logger.warning(f"⚠️ Unknown chunk type: {type(chunk)}")
                text_chunks.append(str(chunk))
        
        # Generate embeddings for all text chunks
        embeddings = self.generate_embeddings_batch(text_chunks)
        
        # Filter out None values and count tokens
        valid_embeddings = []
        total_tokens = 0
        
        for i, (text, embedding) in enumerate(zip(text_chunks, embeddings)):
            if embedding is not None:
                valid_embeddings.append(embedding)
                # Rough token count estimation (words * 1.3 for subword tokens)
                total_tokens += int(len(text.split()) * 1.3)
            else:
                logger.warning(f"⚠️ Failed to embed chunk {i}")
        
        logger.info(f"✅ Generated {len(valid_embeddings)} embeddings ({total_tokens} estimated tokens)")
        return valid_embeddings, total_tokens
    
    def preload_common_queries(self, queries: List[str]):
        """Preload embeddings for common queries to optimize first-time performance."""
        logger.info(f"📦 Preloading {len(queries)} common queries...")
        self.generate_embeddings_batch(queries)
        self._save_persistent_cache()
        logger.info("✅ Common queries preloaded")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache performance statistics."""
        total = self._cache_hits + self._cache_misses
        return {
            "total_entries": len(self._embedding_cache),
            "cache_hits": self._cache_hits,
            "cache_misses": self._cache_misses,
            "hit_ratio": (self._cache_hits / total) if total > 0 else 0,
            "max_size": self._cache_max_size
        }
    
    def clear_cache(self):
        """Clear the embedding cache."""
        self._embedding_cache.clear()
        self._cache_hits = 0
        self._cache_misses = 0
        logger.info("🗑️ Cache cleared")
    
    def shutdown(self):
        """Clean shutdown with cache persistence."""
        logger.info("🔚 Shutting down OptimizedEmbeddingService...")
        self._save_persistent_cache()
        self._executor.shutdown(wait=True)
        logger.info("✅ Shutdown complete")


# Singleton instance getter
_optimized_service_instance = None
_service_lock = threading.Lock()

def get_optimized_embedding_service(model: str = "all-MiniLM-L6-v2") -> OptimizedEmbeddingService:
    """Get or create the optimized embedding service singleton."""
    global _optimized_service_instance
    
    if _optimized_service_instance is None:
        with _service_lock:
            if _optimized_service_instance is None:
                _optimized_service_instance = OptimizedEmbeddingService(model)
                
                # Preload common construction queries
                common_queries = [
                    "site diary",
                    "safety report",
                    "quality inspection",
                    "progress report",
                    "incident report",
                    "material delivery",
                    "weather conditions",
                    "workforce attendance",
                    "equipment status",
                    "construction activity"
                ]
                _optimized_service_instance.preload_common_queries(common_queries)
    
    return _optimized_service_instance