"""
Main document processing service that orchestrates the vectorization pipeline.
"""
from datetime import datetime
from ..models.vectorization import (
    VectorizeDocumentRequest, 
    VectorizeDocumentResponse,
    VectorizedDocument,
    DoclingProcessingInfo
)
from ..utils.document_parser import Document<PERSON>arser, DocumentParsingError
from ..utils.text_chunker import TextChunker
from .embedding_service_optimized import get_optimized_embedding_service
from .vector_service import VectorService
from utils.error_handler import retry_with_exponential_backoff, error_context
import logging

logger = logging.getLogger(__name__)


class DocumentProcessingError(Exception):
    """Exception raised when document processing fails."""
    pass


class DocumentService:
    """Main service for document vectorization pipeline."""
    
    def __init__(self):
        """Initialize document service with dependencies."""
        self.parser = DocumentParser()
        self.chunker = TextChunker()  # Now creates instance with tiktoken support
        self.embedding_service = get_optimized_embedding_service()
        self.vector_service = VectorService()
    
    
    async def process_document(self, request: VectorizeDocumentRequest) -> VectorizeDocumentResponse:
        """
        Process document through the complete vectorization pipeline.
        
        Args:
            request: Vectorization request
            
        Returns:
            Vectorization response with results
        """
        start_time = datetime.now()
        document_id = request.id
        
        logger.info(f"Starting document processing for {document_id}: {request.file_name}")
        
        try:
            with error_context("document_processing", document_id=document_id, project_id=request.project_id):
                # Step 1: Parse document and extract text
                logger.info("Step 1: Parsing document and extracting text")
                text_content, metadata = await self._parse_document_with_retry(str(request.url))
            
                if not text_content.strip():
                    return VectorizeDocumentResponse(
                        success=False,
                        message="No text content could be extracted from document",
                        document_id=document_id,
                        chunks_processed=0,
                        total_tokens=0,
                        processing_time_seconds=0,
                        error="Empty document content"
                    )
            
            # Log enhanced Docling metadata
            docling_enhanced = metadata.get('docling_enhanced', {})
            if docling_enhanced:
                logger.info(
                    f"✅ Docling extraction completed:\n"
                    f"   📝 Characters: {len(text_content)}\n"
                    f"   📄 Pages: {metadata.get('total_pages', 'unknown')}\n"
                    f"   📊 Tables: {docling_enhanced.get('tables_count', 0)}\n"
                    f"   🖼️  Images: {docling_enhanced.get('images_count', 0)}\n"
                    f"   🔍 Method: {docling_enhanced.get('processing_method', 'unknown')}\n"
                    f"   ⏱️  Processing time: {docling_enhanced.get('processing_time_seconds', 0):.2f}s\n"
                    f"   🎯 OCR confidence: {docling_enhanced.get('ocr_confidence', 0):.1%}"
                )
            else:
                logger.info(f"Extracted {len(text_content)} characters from document")
            
            # Step 2: Chunk the text using Markdown-aware chunking
            logger.info("Step 2: Chunking text content with Markdown structure preservation")
            chunks = TextChunker.chunk_by_markdown_sections(
                text=text_content,
                max_chunk_size=2000,  # Larger chunks for better context
                overlap=150,          # Reduced overlap for cleaner boundaries
                document_id=document_id
            )
            
            if not chunks:
                return VectorizeDocumentResponse(
                    success=False,
                    message="Failed to create chunks from document content",
                    document_id=document_id,
                    chunks_processed=0,
                    total_tokens=0,
                    processing_time_seconds=(datetime.now() - start_time).total_seconds(),
                    error="Chunking failed"
                )
            
            logger.info(f"Created {len(chunks)} chunks")
            
            # Step 3: Generate embeddings
            logger.info("Step 3: Generating embeddings")
            embeddings, total_tokens = self.embedding_service.embed_document_chunks(chunks)
            
            # Filter out chunks with failed embeddings
            valid_chunks = []
            valid_embeddings = []
            for chunk, embedding in zip(chunks, embeddings):
                if embedding:  # Only keep chunks with valid embeddings
                    valid_chunks.append(chunk)
                    valid_embeddings.append(embedding)
            
            if not valid_chunks:
                return VectorizeDocumentResponse(
                    success=False,
                    message="Failed to generate embeddings for any chunks",
                    document_id=document_id,
                    chunks_processed=0,
                    total_tokens=total_tokens,
                    processing_time_seconds=(datetime.now() - start_time).total_seconds(),
                    error="Embedding generation failed"
                )
            
            logger.info(f"Generated {len(valid_embeddings)} valid embeddings")
            
            # Step 4: Create vectorized document metadata
            vectorized_doc = VectorizedDocument(
                document_id=document_id,
                project_id=request.project_id,
                file_name=request.file_name,
                category=request.category,
                created_at=request.created_at,
                processed_at=datetime.now(),
                total_chunks=len(valid_chunks),
                total_tokens=total_tokens,
                source_url=str(request.url),
                embedding_model=self.embedding_service.model
            )
            
            # Step 5: Store in Milvus
            logger.info("Step 5: Storing vectors in Milvus")
            milvus_ids = await self.vector_service.store_document_vectors(
                document=vectorized_doc,
                chunks=valid_chunks,
                embeddings=valid_embeddings
            )
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            # Create enhanced Docling information if available
            docling_info = None
            docling_enhanced = metadata.get('docling_enhanced', {})
            if docling_enhanced:
                docling_info = DoclingProcessingInfo(
                    processing_method=docling_enhanced.get('processing_method', 'text_extraction'),
                    total_pages=metadata.get('total_pages', 1),
                    tables_count=docling_enhanced.get('tables_count', 0),
                    images_count=docling_enhanced.get('images_count', 0),
                    layout_elements_count=docling_enhanced.get('layout_elements_count', 0),
                    ocr_confidence=docling_enhanced.get('ocr_confidence', 1.0),
                    document_type=docling_enhanced.get('document_type', 'pdf'),
                    export_format=docling_enhanced.get('export_format', 'markdown')
                )
            
            logger.info(
                f"Document processing completed successfully in {processing_time:.2f}s. "
                f"Processed {len(valid_chunks)} chunks with {total_tokens} tokens"
            )
            
            return VectorizeDocumentResponse(
                success=True,
                message=f"Document successfully vectorized with {len(valid_chunks)} chunks",
                document_id=document_id,
                chunks_processed=len(valid_chunks),
                total_tokens=total_tokens,
                processing_time_seconds=processing_time,
                milvus_ids=milvus_ids,
                docling_info=docling_info
            )
            
        except DocumentParsingError as e:
            logger.error(f"Document parsing failed: {str(e)}")
            return VectorizeDocumentResponse(
                success=False,
                message="Failed to parse document",
                document_id=document_id,
                chunks_processed=0,
                total_tokens=0,
                processing_time_seconds=(datetime.now() - start_time).total_seconds(),
                error=str(e)
            )
            
        except Exception as e:
            logger.error(f"Document processing failed: {str(e)}")
            return VectorizeDocumentResponse(
                success=False,
                message="Document processing failed",
                document_id=document_id,
                chunks_processed=0,
                total_tokens=0,
                processing_time_seconds=(datetime.now() - start_time).total_seconds(),
                error=str(e)
            )
    
    async def check_document_exists(self, document_id: str) -> bool:
        """
        Check if document has already been vectorized.
        
        Args:
            document_id: Document identifier
            
        Returns:
            True if document exists in vector database
        """
        return await self.vector_service.document_exists(document_id)
    
    @retry_with_exponential_backoff(max_retries=3, exceptions=(Exception,))
    async def _parse_document_with_retry(self, url: str):
        """Parse document with retry logic."""
        parsed_doc = self.parser.parse_document(url)
        
        # Convert ParsedDocument to legacy tuple format (text_content, metadata)
        legacy_metadata = {
            'total_pages': parsed_doc.total_pages,
            'pages': [
                {
                    'page_number': page['page_number'],
                    'char_count': page['char_count']
                }
                for page in parsed_doc.pages
            ],
            # Enhanced metadata
            'docling_enhanced': {
                'processing_method': parsed_doc.processing_method,
                'tables_count': len(parsed_doc.tables),
                'images_count': len(parsed_doc.images),
                'layout_elements_count': len(parsed_doc.layout_elements),
                'processing_time_seconds': parsed_doc.processing_time_seconds,
                'ocr_confidence': parsed_doc.ocr_confidence,
                'document_type': parsed_doc.document_type,
                'export_format': parsed_doc.metadata.get('export_format', 'markdown')
            }
        }
        
        return parsed_doc.text_content, legacy_metadata
    
    def get_processing_stats(self) -> dict:
        """
        Get processing statistics.
        
        Returns:
            Dictionary with processing stats
        """
        return {
            "embedding_model": self.embedding_service.model,
            "embedding_dimension": self.embedding_service.get_embedding_dimension(),
            "vector_collection": self.vector_service.collection_name,
            "chunking_strategy": "sentence-based",
            "max_chunk_size": 1000,
            "chunk_overlap": 200
        }