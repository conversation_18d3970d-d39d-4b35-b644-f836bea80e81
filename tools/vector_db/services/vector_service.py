"""
Vector storage service for Milvus integration.
"""
import hashlib
from typing import List, Dict, Any, Optional
from ..models.vectorization import VectorizedDocument, DocumentChunk
from ..milvus_client_optimized import get_optimized_vector_db
from utils.cache import cache_vector_search_results, cached
import logging

logger = logging.getLogger(__name__)


class VectorStorageError(Exception):
    """Exception raised when vector storage operations fail."""
    pass


class VectorService:
    """Service for vector storage operations in Milvus."""
    
    def __init__(self, collection_name: str = "document_vectors"):
        """
        Initialize vector service.
        
        Args:
            collection_name: Milvus collection name
        """
        self.collection_name = collection_name
        self.vector_db = get_optimized_vector_db()
    
    async def store_document_vectors(
        self,
        document: VectorizedDocument,
        chunks: List[DocumentChunk],
        embeddings: List[List[float]]
    ) -> List[str]:
        """
        Store document vectors in Milvus.
        
        Args:
            document: Vectorized document metadata
            chunks: Document chunks
            embeddings: Corresponding embeddings
            
        Returns:
            List of Milvus record IDs
            
        Raises:
            VectorStorageError: If storage fails
        """
        if len(chunks) != len(embeddings):
            raise VectorStorageError(
                f"Chunk count ({len(chunks)}) doesn't match embedding count ({len(embeddings)})"
            )
        
        logger.info(f"Storing {len(chunks)} vectors for document {document.document_id}")
        
        try:
            # Prepare data for Milvus insertion
            vector_records = []
            
            for chunk, embedding in zip(chunks, embeddings):
                # Generate unique ID for each chunk
                chunk_hash = hashlib.md5(
                    f"{document.document_id}:{chunk.chunk_index}:{chunk.content[:100]}"
                    .encode()
                ).hexdigest()
                
                record = {
                    'id': chunk_hash,
                    'vector': embedding,  # Changed from 'embedding' to 'vector' to match schema
                    'document_id': document.document_id,
                    'name': document.file_name,
                    'file_url': document.source_url,
                    'category': document.category,
                    'added_by': 'vectorization_service',
                    # FIXED: Correct date field mapping for proper filtering
                    'submitted_at': document.created_at.isoformat()[:50],  # When document was originally created/submitted
                    'created_at': document.created_at.isoformat()[:50],   # When document was originally created
                    'workspace_group': document.category,
                    'project_id': document.project_id,
                    'document_type': self._map_category_to_type(document.category),
                    'content_text': chunk.content,
                    'chunk_index': chunk.chunk_index,
                    'page_number': chunk.page_number,
                    'token_count': chunk.token_count,
                    'embedding_model': document.embedding_model
                }
                
                vector_records.append(record)
            
            # Insert into Milvus
            success = self.vector_db.insert_documents(vector_records)
            
            if not success:
                raise VectorStorageError("Failed to insert documents into Milvus")
            
            # Return the IDs of inserted records
            record_ids = [record['id'] for record in vector_records]
            
            logger.info(f"Successfully stored {len(record_ids)} vectors in Milvus")
            return record_ids
            
        except Exception as e:
            logger.error(f"Failed to store vectors in Milvus: {str(e)}")
            raise VectorStorageError(f"Vector storage failed: {str(e)}")
    
    async def document_exists(self, document_id: str) -> bool:
        """
        Check if document already exists in vector database.
        
        Args:
            document_id: Document identifier
            
        Returns:
            True if document exists
        """
        try:
            logger.debug(f"Checking if document exists: {document_id}")
            
            # Query Milvus for any records with this document_id
            # Use a dummy embedding vector for the search (we only care about filter)
            dummy_embedding = [0.0] * 384  # Match all-MiniLM-L6-v2 embedding dimension
            
            results = self.vector_db.search_documents(
                query_embedding=dummy_embedding,
                project_id="",  # Empty to search all projects
                limit=1  # We only need to know if any exist
            )
            
            # Filter results by document_id (since Milvus search might return other docs)
            matching_docs = [doc for doc in results if doc.get('document_id') == document_id]
            
            exists = len(matching_docs) > 0
            logger.debug(f"Document {document_id} exists: {exists}")
            
            return exists
            
        except Exception as e:
            logger.error(f"Error checking document existence for {document_id}: {str(e)}")
            # Return False on error to allow processing (safer default)
            return False
    
    async def get_document_chunks(self, document_id: str) -> List[Dict[str, Any]]:
        """
        Retrieve all chunks for a document.
        
        Args:
            document_id: Document identifier
            
        Returns:
            List of document chunks with metadata
        """
        try:
            logger.debug(f"Retrieving chunks for document: {document_id}")
            
            # Use a dummy embedding for search (we only care about filtering by document_id)
            dummy_embedding = [0.0] * 384
            
            # Search with a high limit to get all chunks for this document
            results = self.vector_db.search_documents(
                query_embedding=dummy_embedding,
                project_id="",  # Empty to search all projects
                limit=1000  # High limit to get all chunks
            )
            
            # Filter results by document_id and sort by chunk_index
            document_chunks = [
                doc for doc in results 
                if doc.get('document_id') == document_id
            ]
            
            # Sort chunks by chunk_index for proper ordering
            document_chunks.sort(key=lambda x: x.get('chunk_index', 0))
            
            logger.info(f"Retrieved {len(document_chunks)} chunks for document {document_id}")
            
            # Return chunks with all their metadata
            return document_chunks
            
        except Exception as e:
            logger.error(f"Error retrieving document chunks for {document_id}: {str(e)}")
            return []
    
    async def delete_document(self, document_id: str) -> bool:
        """
        Delete all chunks for a document.
        
        Args:
            document_id: Document identifier
            
        Returns:
            True if deletion was successful
        """
        try:
            logger.info(f"Attempting to delete document: {document_id}")
            
            # First, check if document exists and get chunk count
            chunks = await self.get_document_chunks(document_id)
            if not chunks:
                logger.warning(f"No chunks found for document {document_id}")
                return True  # Nothing to delete, consider it successful
            
            logger.info(f"Found {len(chunks)} chunks to delete for document {document_id}")
            
            # Delete all chunks for this document using the document_id filter
            # The milvus_client.delete_documents method uses document_id filter
            success = self.vector_db.delete_documents([document_id])
            
            if success:
                logger.info(f"Successfully deleted {len(chunks)} chunks for document {document_id}")
                
                # Verify deletion by checking if document still exists
                still_exists = await self.document_exists(document_id)
                if still_exists:
                    logger.warning(f"Document {document_id} may not be fully deleted")
                    return False
                else:
                    logger.info(f"Confirmed document {document_id} fully deleted")
                    return True
            else:
                logger.error(f"Failed to delete document {document_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error deleting document {document_id}: {str(e)}")
            return False
    
    @cached(ttl=60, key_prefix="collection_stats")  # Cache for 1 minute
    def get_collection_stats(self) -> Dict[str, Any]:
        """
        Get vector collection statistics.
        
        Returns:
            Collection statistics
        """
        try:
            return self.vector_db.get_collection_stats()
        except Exception as e:
            logger.error(f"Error getting collection stats: {str(e)}")
            return {
                "error": str(e),
                "collection_name": self.collection_name,
                "total_documents": 0
            }
    
    async def get_document_info(self, document_id: str) -> Dict[str, Any]:
        """
        Get comprehensive information about a document.
        
        Args:
            document_id: Document identifier
            
        Returns:
            Document information including chunk count and metadata
        """
        try:
            chunks = await self.get_document_chunks(document_id)
            
            if not chunks:
                return {
                    "document_id": document_id,
                    "exists": False,
                    "chunk_count": 0
                }
            
            # Get metadata from first chunk (document-level info)
            first_chunk = chunks[0]
            
            return {
                "document_id": document_id,
                "exists": True,
                "chunk_count": len(chunks),
                "file_name": first_chunk.get("name", ""),
                "category": first_chunk.get("category", ""),
                "document_type": first_chunk.get("document_type", ""),
                "created_at": first_chunk.get("created_at", ""),
                "submitted_at": first_chunk.get("submitted_at", ""),
                "embedding_model": first_chunk.get("embedding_model", ""),
                "total_tokens": sum(chunk.get("token_count", 0) for chunk in chunks),
                "source_url": first_chunk.get("file_url", "")
            }
            
        except Exception as e:
            logger.error(f"Error getting document info for {document_id}: {str(e)}")
            return {
                "document_id": document_id,
                "exists": False,
                "error": str(e)
            }
    
    @cache_vector_search_results(ttl=300)  # Cache for 5 minutes
    async def search_documents_by_content(
        self,
        query_embedding: List[float],
        document_id_filter: Optional[str] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        Search for documents by content similarity with optional document ID filtering.
        
        Args:
            query_embedding: Query embedding vector
            document_id_filter: Optional document ID to filter by
            limit: Maximum results to return
            
        Returns:
            List of matching documents with similarity scores
        """
        try:
            # Use the enhanced search that supports empty project_id
            results = self.vector_db.search_documents(
                query_embedding=query_embedding,
                project_id="",  # Empty to search all projects
                limit=limit
            )
            
            # Filter by document_id if specified
            if document_id_filter:
                results = [doc for doc in results if doc.get('document_id') == document_id_filter]
            
            return results
            
        except Exception as e:
            logger.error(f"Error searching documents by content: {str(e)}")
            return []
    
    @staticmethod
    def _map_category_to_type(category: str) -> str:
        """
        Map document category to document type.
        
        Args:
            category: Document category
            
        Returns:
            Mapped document type
        """
        category_lower = category.lower()
        
        if 'safety' in category_lower or 'manual' in category_lower:
            return 'safety_document'
        elif 'report' in category_lower:
            return 'report'
        elif 'drawing' in category_lower or 'blueprint' in category_lower:
            return 'drawing'
        elif 'diary' in category_lower:
            return 'site_diary'
        elif 'specification' in category_lower or 'spec' in category_lower:
            return 'specification'
        else:
            return 'document'