"""
Pydantic models for document vectorization.
"""
from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field, HttpUrl


class VectorizeDocumentRequest(BaseModel):
    """Request model for document vectorization."""
    
    id: str = Field(..., description="Unique document identifier")
    project_id: str = Field(..., description="Project identifier for organizing documents")
    url: HttpUrl = Field(..., description="URL of the document to vectorize")
    file_name: str = Field(..., description="Name of the document file")
    created_at: datetime = Field(..., description="Document creation timestamp")
    category: str = Field(..., description="Document category (e.g., safety_manual, report, drawing)")


class DoclingProcessingInfo(BaseModel):
    """Enhanced processing information from Docling."""
    
    processing_method: str = Field(..., description="Processing method used (ocr, text_extraction, layout_analysis)")
    total_pages: int = Field(..., description="Total number of pages processed")
    tables_count: int = Field(default=0, description="Number of tables detected")
    images_count: int = Field(default=0, description="Number of images detected") 
    layout_elements_count: int = Field(default=0, description="Total layout elements detected")
    ocr_confidence: float = Field(default=1.0, description="OCR confidence score (0-1)")
    document_type: str = Field(..., description="Detected document type")
    export_format: str = Field(default="markdown", description="Export format used")


class VectorizeDocumentResponse(BaseModel):
    """Enhanced response model for document vectorization with Docling features."""
    
    success: bool = Field(..., description="Whether vectorization was successful")
    message: str = Field(..., description="Status message")
    document_id: str = Field(..., description="Unique document identifier")
    chunks_processed: int = Field(..., description="Number of text chunks created")
    total_tokens: int = Field(..., description="Total tokens processed for embeddings")
    processing_time_seconds: float = Field(..., description="Time taken to process document")
    milvus_ids: List[str] = Field(default_factory=list, description="Milvus record IDs")
    error: Optional[str] = Field(None, description="Error message if failed")
    
    # Enhanced Docling information
    docling_info: Optional[DoclingProcessingInfo] = Field(None, description="Enhanced processing details from Docling")


class DocumentChunk(BaseModel):
    """Model for document text chunks."""
    
    chunk_id: str = Field(..., description="Unique chunk identifier")
    content: str = Field(..., description="Chunk text content")
    chunk_index: int = Field(..., description="Index of chunk in document")
    page_number: Optional[int] = Field(None, description="Page number if applicable")
    token_count: int = Field(..., description="Number of tokens in chunk")


class VectorizedDocument(BaseModel):
    """Model for a vectorized document with metadata."""
    
    document_id: str = Field(..., description="Document unique identifier")
    project_id: str = Field(..., description="Project identifier for organizing documents")
    file_name: str = Field(..., description="Original file name")
    category: str = Field(..., description="Document category")
    created_at: datetime = Field(..., description="Document creation timestamp")
    processed_at: datetime = Field(..., description="Vectorization processing timestamp")
    total_chunks: int = Field(..., description="Total number of chunks")
    total_tokens: int = Field(..., description="Total tokens processed")
    source_url: str = Field(..., description="Original document URL")
    embedding_model: str = Field(..., description="Model used for embeddings")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }