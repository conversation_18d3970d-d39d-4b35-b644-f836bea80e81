"""
Optimized Milvus client with connection pooling, query caching, and performance enhancements.
"""
import os
import time
import threading
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from collections import OrderedDict
import hashlib
import json
import logging
import re

from pymilvus import MilvusClient, DataType
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger(__name__)


def extract_diary_date_from_name(document_name: str) -> Optional[datetime]:
    """
    Extract the actual diary date from document names with multiple format support.

    Supported formats:
    - "Site Diary - 250224-F1irT.pdf" (YYMMDD format)
    - "Site Diary 25-08-2025-FN-1v.pdf" (DD-MM-YYYY format)
    - "Site Diary - 250226-Y-8al.pdf" (YYMMDD with extra parts)

    Args:
        document_name: Name of the document

    Returns:
        datetime object if date found, None otherwise
    """
    if not document_name:
        return None

    try:
        # Pattern 1: "Site Diary - YYMMDD-..." format (most common in your data)
        pattern1 = r'Site\s+Diary\s*-\s*(\d{2})(\d{2})(\d{2})'
        match1 = re.search(pattern1, document_name, re.IGNORECASE)

        if match1:
            year_short, month, day = match1.groups()
            # Convert 2-digit year to 4-digit (assuming 20xx)
            year = 2000 + int(year_short)
            diary_date = datetime(year, int(month), int(day))
            logger.debug(f"📅 Extracted diary date {diary_date.strftime('%Y-%m-%d')} from YYMMDD format: {document_name}")
            return diary_date

        # Pattern 2: "Site Diary DD-MM-YYYY-..." format (legacy format)
        pattern2 = r'Site\s+Diary\s+(\d{2})-(\d{2})-(\d{4})'
        match2 = re.search(pattern2, document_name, re.IGNORECASE)

        if match2:
            day, month, year = match2.groups()
            diary_date = datetime(int(year), int(month), int(day))
            logger.debug(f"📅 Extracted diary date {diary_date.strftime('%Y-%m-%d')} from DD-MM-YYYY format: {document_name}")
            return diary_date

        # Pattern 3: "Site Diary YYYYMMDD" format (but validate month/day ranges)
        pattern3 = r'Site\s+Diary\s*-?\s*(\d{4})(\d{2})(\d{2})'
        match3 = re.search(pattern3, document_name, re.IGNORECASE)

        if match3:
            year, month, day = match3.groups()
            # Validate month and day ranges
            if 1 <= int(month) <= 12 and 1 <= int(day) <= 31:
                diary_date = datetime(int(year), int(month), int(day))
                logger.debug(f"📅 Extracted diary date {diary_date.strftime('%Y-%m-%d')} from YYYYMMDD format: {document_name}")
                return diary_date
            else:
                logger.debug(f"⚠️ Invalid month ({month}) or day ({day}) in YYYYMMDD format: {document_name}")
                # Fall through to try other patterns

        logger.debug(f"⚠️ Could not extract diary date from document name: {document_name}")
        return None

    except Exception as e:
        logger.warning(f"❌ Error extracting date from document name '{document_name}': {e}")
        return None


def is_document_in_date_range(document_name: str, start_date: str, end_date: str) -> bool:
    """
    Check if a document's diary date (extracted from name) falls within the specified range.
    
    Args:
        document_name: Name of the document
        start_date: Start date in ISO format (e.g., "2025-08-18T00:00:00+00:00")
        end_date: End date in ISO format (e.g., "2025-08-25T23:59:59+00:00")
        
    Returns:
        True if document falls within date range, False otherwise
    """
    if not start_date or not end_date:
        logger.debug("📅 No date range specified, including document")
        return True
    
    diary_date = extract_diary_date_from_name(document_name)
    if not diary_date:
        logger.debug(f"📅 Could not extract diary date from '{document_name}', INCLUDING as fallback")
        return True  # Include documents where we can't extract diary date
    
    try:
        # Parse the start and end dates
        start_dt = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
        end_dt = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
        
        # Check if diary date falls within range
        is_in_range = start_dt.date() <= diary_date.date() <= end_dt.date()
        
        logger.debug(f"📅 Document '{document_name}' diary date {diary_date.strftime('%Y-%m-%d')} "
                    f"{'INCLUDED' if is_in_range else 'EXCLUDED'} for range {start_dt.strftime('%Y-%m-%d')} to {end_dt.strftime('%Y-%m-%d')}")
        
        return is_in_range
        
    except Exception as e:
        logger.warning(f"❌ Error checking date range for '{document_name}': {e}")
        return True  # Include documents on error as fallback


class MilvusConnectionPool:
    """Connection pool for Milvus to reduce connection overhead."""
    
    def __init__(self, uri: str, max_connections: int = 5):
        self.uri = uri
        self.max_connections = max_connections
        self._connections = []
        self._available = []
        self._lock = threading.Lock()
        
        # Pre-create connections
        for _ in range(min(2, max_connections)):
            self._create_connection()
    
    def _create_connection(self) -> MilvusClient:
        """Create a new Milvus connection."""
        try:
            client = MilvusClient(uri=self.uri)
            self._connections.append(client)
            self._available.append(client)
            return client
        except Exception as e:
            logger.error(f"Failed to create Milvus connection: {e}")
            raise
    
    def get_connection(self) -> MilvusClient:
        """Get an available connection from the pool."""
        with self._lock:
            # Return available connection
            if self._available:
                return self._available.pop()
            
            # Create new connection if under limit
            if len(self._connections) < self.max_connections:
                client = self._create_connection()
                self._available.remove(client)  # Mark as in use
                return client
            
            # Wait for available connection
            retries = 0
            while not self._available and retries < 10:
                time.sleep(0.1)
                retries += 1
            
            if self._available:
                return self._available.pop()
            
            # Fallback: create temporary connection
            logger.warning("Connection pool exhausted, creating temporary connection")
            return MilvusClient(uri=self.uri)
    
    def return_connection(self, client: MilvusClient):
        """Return a connection to the pool."""
        with self._lock:
            if client in self._connections and client not in self._available:
                self._available.append(client)
    
    def close_all(self):
        """Close all connections in the pool."""
        with self._lock:
            for client in self._connections:
                try:
                    client.close()
                except:
                    pass
            self._connections.clear()
            self._available.clear()


class OptimizedVectorDB:
    """Optimized vector database client with multiple performance enhancements."""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls, collection_name: str = "project_documents"):
        """Thread-safe singleton pattern."""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self, collection_name: str = "project_documents"):
        """Initialize with performance optimizations."""
        if self._initialized:
            return
            
        with self._lock:
            if self._initialized:
                return
            
            self.collection_name = collection_name
            # Use environment variables for Milvus connection
            milvus_host = os.getenv("MILVUS_HOST", "milvus")
            milvus_port = os.getenv("MILVUS_PORT", "19530")
            
            # Use the configured host directly (works for both Docker and local)
            self.uri = os.getenv("MILVUS_URI", f"http://{milvus_host}:{milvus_port}")
            
            # Fallback URIs to try if primary fails
            self.fallback_uris = [
                f"http://127.0.0.1:{milvus_port}",
                f"http://localhost:{milvus_port}"
            ]
            
            # Query result cache (LRU)
            self._query_cache = OrderedDict()
            self._cache_max_size = 100
            self._cache_ttl = 300  # 5 minutes
            
            # Performance metrics
            self._metrics = {
                "total_queries": 0,
                "cache_hits": 0,
                "cache_misses": 0,
                "total_query_time": 0,
                "query_times": []  # Rolling window of last 100 query times
            }
            
            # Thread pool for parallel operations
            self._executor = ThreadPoolExecutor(max_workers=4)
            
            # Initialize connection - this will set correct URI and create pool
            self._test_connection()

            # Ensure collection exists
            self._ensure_collection_exists()

            self._initialized = True
            
            logger.info("✅ OptimizedVectorDB initialized")
            logger.info(f"   - Collection: {collection_name}")
            logger.info(f"   - URI: {self.uri}")
            logger.info("   - Connection pool size: 5")
            logger.info(f"   - Query cache size: {self._cache_max_size}")
    
    def _test_connection(self):
        """Test Milvus connection with fallback URIs."""
        last_error = None
        working_uri = None
        
        # Try primary URI first
        try:
            logger.info(f"🔄 Testing primary URI: {self.uri}")
            test_client = MilvusClient(uri=self.uri)
            collections = test_client.list_collections()
            test_client.close()
            logger.info(f"✅ Primary Milvus URI {self.uri} works. Collections: {len(collections)}")
            working_uri = self.uri
        except Exception as e:
            last_error = e
            logger.warning(f"⚠️ Primary Milvus URI {self.uri} failed: {e}")
        
        # Try fallback URIs if primary failed
        if not working_uri:
            for fallback_uri in self.fallback_uris:
                try:
                    logger.info(f"🔄 Testing fallback URI: {fallback_uri}")
                    test_client = MilvusClient(uri=fallback_uri)
                    collections = test_client.list_collections()
                    test_client.close()
                    logger.info(f"✅ Fallback Milvus URI {fallback_uri} works. Collections: {len(collections)}")
                    working_uri = fallback_uri
                    self.uri = fallback_uri  # Update to use the working URI
                    break
                except Exception as e:
                    last_error = e
                    logger.warning(f"⚠️ Fallback URI {fallback_uri} failed: {e}")
        
        # Create connection pool with working URI
        if working_uri:
            logger.info(f"🔧 Creating connection pool for {working_uri}")
            self._pool = MilvusConnectionPool(working_uri, max_connections=5)
            logger.info(f"✅ Milvus connection established at {working_uri}")
        else:
            # All connections failed
            logger.error(f"❌ All Milvus connection attempts failed. Last error: {last_error}")
            raise last_error

    def _ensure_collection_exists(self):
        """Ensure the collection exists, create if it doesn't."""
        try:
            client = self._pool.get_connection()
            try:
                # Check if collection exists
                collections = client.list_collections()
                if self.collection_name in collections:
                    logger.info(f"✅ Collection '{self.collection_name}' already exists")
                    return

                logger.info(f"🔧 Creating collection '{self.collection_name}'...")

                # Define collection schema for document vectors
                schema = client.create_schema(
                    auto_id=False,
                    enable_dynamic_field=True,
                    description="Document vectors for semantic search"
                )

                # Add fields to schema
                schema.add_field(field_name="id", datatype=DataType.VARCHAR, max_length=255, is_primary=True)
                schema.add_field(field_name="vector", datatype=DataType.FLOAT_VECTOR, dim=384)  # all-MiniLM-L6-v2 dimension
                schema.add_field(field_name="document_id", datatype=DataType.VARCHAR, max_length=255)
                schema.add_field(field_name="project_id", datatype=DataType.VARCHAR, max_length=255)
                schema.add_field(field_name="name", datatype=DataType.VARCHAR, max_length=500)
                schema.add_field(field_name="file_url", datatype=DataType.VARCHAR, max_length=1000)
                schema.add_field(field_name="category", datatype=DataType.VARCHAR, max_length=100)
                schema.add_field(field_name="added_by", datatype=DataType.VARCHAR, max_length=255)
                schema.add_field(field_name="submitted_at", datatype=DataType.VARCHAR, max_length=50)
                schema.add_field(field_name="created_at", datatype=DataType.VARCHAR, max_length=50)
                schema.add_field(field_name="workspace_group", datatype=DataType.VARCHAR, max_length=255)
                schema.add_field(field_name="content_text", datatype=DataType.VARCHAR, max_length=65535)
                schema.add_field(field_name="document_type", datatype=DataType.VARCHAR, max_length=50)

                # Create collection
                client.create_collection(
                    collection_name=self.collection_name,
                    schema=schema,
                    description="Document vectors for semantic search and RAG"
                )

                # Create index for vector field
                index_params = client.prepare_index_params()
                index_params.add_index(
                    field_name="vector",
                    index_type="IVF_FLAT",
                    metric_type="COSINE",
                    params={"nlist": 1024}
                )

                client.create_index(
                    collection_name=self.collection_name,
                    index_params=index_params
                )

                # Load collection
                client.load_collection(collection_name=self.collection_name)

                logger.info(f"✅ Collection '{self.collection_name}' created and loaded successfully")

            finally:
                self._pool.return_connection(client)

        except Exception as e:
            logger.error(f"❌ Failed to ensure collection exists: {str(e)}")
            raise

    def _get_cache_key(self, **kwargs) -> str:
        """Generate cache key for query parameters."""
        # Create deterministic key from parameters
        key_dict = {k: v for k, v in sorted(kwargs.items()) if v is not None}
        key_string = json.dumps(key_dict, sort_keys=True, default=str)
        return hashlib.sha256(key_string.encode()).hexdigest()[:32]
    
    def _is_cache_valid(self, timestamp: float) -> bool:
        """Check if cache entry is still valid."""
        return (time.time() - timestamp) < self._cache_ttl
    
    def _update_cache(self, key: str, value: Any):
        """Update query cache with LRU eviction."""
        # Add/update entry
        if key in self._query_cache:
            del self._query_cache[key]
        
        self._query_cache[key] = {
            "value": value,
            "timestamp": time.time()
        }
        
        # Manage cache size
        if len(self._query_cache) > self._cache_max_size:
            # Remove oldest entry
            self._query_cache.popitem(last=False)
    
    def _update_metrics(self, query_time: float, cache_hit: bool = False):
        """Update performance metrics."""
        self._metrics["total_queries"] += 1
        
        if cache_hit:
            self._metrics["cache_hits"] += 1
        else:
            self._metrics["cache_misses"] += 1
            self._metrics["total_query_time"] += query_time
            
            # Maintain rolling window of query times
            self._metrics["query_times"].append(query_time)
            if len(self._metrics["query_times"]) > 100:
                self._metrics["query_times"].pop(0)
    
    def search_documents(
        self,
        query_embedding: List[float],
        project_id: str,
        document_type: Optional[str] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        workspace_group: Optional[str] = None,
        limit: int = 100,
        use_cache: bool = True
    ) -> List[Dict[str, Any]]:
        """
        Search documents with the same interface as the original MilvusClient.
        This is a compatibility wrapper for the optimized search_similar method.
        """
        # Build filters dict
        filters = {}
        if start_date:
            filters['start_date'] = start_date
        if end_date:
            filters['end_date'] = end_date
        if document_type:
            filters['document_type'] = document_type
        if workspace_group:
            filters['workspace_group'] = workspace_group
        
        # Call the optimized search method
        result = self.search_similar(
            embedding=query_embedding,
            project_id=project_id,
            limit=limit,
            filters=filters,
            use_cache=use_cache
        )
        
        # Unpack result tuple
        if isinstance(result, tuple) and len(result) == 2:
            documents, query_time = result
            logger.debug(f"🔍 Unpacked tuple result: {len(documents)} documents, {query_time:.3f}s")
        else:
            # Fallback - assume result is just documents
            documents = result if isinstance(result, list) else []
            query_time = 0.0
            logger.warning(f"⚠️ Unexpected result format from search_similar: {type(result)}")
        
        # Ensure we're returning a list of dictionaries
        if not isinstance(documents, list):
            logger.error(f"❌ Documents is not a list: {type(documents)}")
            documents = []
            
        # Validate each document is a dictionary
        valid_documents = []
        for i, doc in enumerate(documents):
            if isinstance(doc, dict):
                valid_documents.append(doc)
            else:
                logger.warning(f"⚠️ Skipping invalid document at index {i}: {type(doc)}")
        
        documents = valid_documents
        
        # Log performance
        logger.info(f"⚡ Optimized search completed for project '{project_id}': {len(documents)} docs in {query_time:.3f}s")
        
        return documents
    
    def search_similar(
        self,
        embedding: List[float],
        project_id: str,
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None,
        use_cache: bool = True
    ) -> Tuple[List[Dict[str, Any]], float]:
        """
        Optimized vector similarity search with caching.
        
        Returns:
            Tuple of (results, query_time_seconds)
        """
        start_time = time.time()
        
        # Check cache if enabled
        if use_cache:
            cache_key = self._get_cache_key(
                embedding_hash=hashlib.sha256(str(embedding).encode()).hexdigest()[:16],
                project_id=project_id,
                limit=limit,
                filters=filters
            )
            
            if cache_key in self._query_cache:
                cache_entry = self._query_cache[cache_key]
                if self._is_cache_valid(cache_entry["timestamp"]):
                    # Move to end (most recently used)
                    self._query_cache.move_to_end(cache_key)
                    self._update_metrics(0, cache_hit=True)
                    logger.debug(f"✨ Cache hit for project {project_id}")
                    # Return the cached tuple directly
                    return cache_entry["value"]
        
        # Build filter expression
        filter_expr = f'project_id == "{project_id}"'
        
        if filters:
            # Handle different types of date filtering

            # Option 1: Direct submitted_at filtering (when specified)
            if filters.get("submitted_start") and filters.get("submitted_end"):
                filter_expr += f' and submitted_at >= "{filters["submitted_start"]}"'
                filter_expr += f' and submitted_at <= "{filters["submitted_end"]}"'
                logger.info(f"📅 Using direct submitted_at filtering: {filters['submitted_start']} to {filters['submitted_end']}")

            # Option 2: Custom filter expression (for complex queries)
            elif filters.get("custom_filter"):
                filter_expr += f' and ({filters["custom_filter"]})'
                logger.info(f"🔧 Using custom filter: {filters['custom_filter']}")

            # Option 3: Standard date filtering using submitted_at (primary behavior)
            elif filters.get("start_date") and filters.get("end_date"):
                # Use submitted_at field directly for date range filtering
                start_date = filters["start_date"]
                end_date = filters["end_date"]

                filter_expr += f' and submitted_at >= "{start_date}"'
                filter_expr += f' and submitted_at <= "{end_date}"'
                logger.info(f"📅 Using submitted_at date filtering: {start_date} to {end_date}")
            
            if filters.get("document_type"):
                filter_expr += f' and category == "{filters["document_type"]}"'
            
            if filters.get("workspace_group"):
                filter_expr += f' and workspace_group == "{filters["workspace_group"]}"'
        
        # Execute search with connection from pool
        client = self._pool.get_connection()
        try:
            logger.debug(f"🔍 Executing Milvus search with filter: {filter_expr}")
            
            results = client.search(
                collection_name=self.collection_name,
                data=[embedding],
                filter=filter_expr,
                limit=limit * 2,  # Get extra results for client-side filtering
                output_fields=[
                    "document_id", "name", "file_url", "category", 
                    "added_by", "submitted_at", "created_at", 
                    "workspace_group", "content_text", "document_type"
                ],
                search_params={
                    "metric_type": "COSINE",
                    "params": {
                        "nprobe": 10,
                        "radius": 0.01  # Lower similarity threshold to include more documents
                    }
                },
                timeout=10
            )
            
            # Process results - handle different Milvus result formats
            documents = []
            if results and len(results) > 0:
                logger.debug(f"🔍 Raw results structure: type={type(results)}, len={len(results)}")
                logger.debug(f"🔍 First result type: {type(results[0])}")
                
                # Handle different result formats from Milvus
                result_data = results[0]
                
                # Case 1: Results is a list of Hit objects or dicts
                if hasattr(result_data, '__iter__') and not isinstance(result_data, (str, bytes)):
                    for i, hit in enumerate(result_data):
                        try:
                            logger.debug(f"🔍 Processing hit {i}: type={type(hit)}, is_dict={isinstance(hit, dict)}")
                            
                            # Convert hit to dict if it's not already
                            if not isinstance(hit, dict):
                                # This will be handled by the pymilvus Hit object processing below
                                pass
                            
                            # Handle pymilvus Hit objects
                            if hasattr(hit, 'entity') and hasattr(hit, 'distance'):
                                # This is a pymilvus Hit object - the entity contains the document fields
                                # The hit object itself contains the document data, not hit.entity
                                
                                # Try to get document data directly from the hit
                                if hasattr(hit, 'get') and callable(hit.get):
                                    # Hit object acts like a dict - extract all document fields
                                    entity_data = {}
                                    
                                    # Common document fields to extract
                                    field_names = [
                                        'document_id', 'name', 'file_url', 'category', 
                                        'added_by', 'submitted_at', 'created_at', 
                                        'workspace_group', 'content_text', 'document_type'
                                    ]
                                    
                                    for field_name in field_names:
                                        value = hit.get(field_name)
                                        if value is not None:
                                            entity_data[field_name] = value
                                    
                                    # Add similarity score
                                    entity_data["similarity_score"] = float(hit.distance)
                                    
                                    # Debug logging for first document
                                    if i == 0:
                                        logger.debug(f"🔍 Extracted Hit fields: {list(entity_data.keys())}")
                                        logger.debug(f"🔍 Hit entity has {len(entity_data)} fields")
                                        if 'name' in entity_data:
                                            logger.debug(f"🔍 Document name: {entity_data['name']}")
                                        if 'content_text' in entity_data:
                                            content_length = len(str(entity_data['content_text']))
                                            logger.debug(f"🔍 Content text length: {content_length} chars")
                                    
                                    documents.append(entity_data)
                                    logger.debug(f"✅ Processed Hit object: {entity_data.get('name', 'Unknown')}")
                                else:
                                    logger.warning(f"⚠️ Hit object doesn't support .get() method: {type(hit)}")
                                continue
                            
                            # Handle dict with entity field
                            elif isinstance(hit, dict) and "entity" in hit:
                                doc = hit["entity"]
                                if isinstance(doc, dict):
                                    doc["similarity_score"] = float(hit.get("distance", 0))
                                    documents.append(doc)
                                    logger.debug(f"✅ Processed dict hit with entity: {doc.get('name', 'Unknown')}")
                                else:
                                    logger.warning(f"⚠️ Hit entity is not a dict: {type(doc)}")
                                continue
                                
                            elif isinstance(hit, dict) and "entity" not in hit:
                                # Dict without entity field - check if it's a document
                                if 'document_id' in hit or 'name' in hit:
                                    # This looks like a document, use it directly
                                    hit["similarity_score"] = hit.get("similarity_score", 0.0)  # Default score if missing
                                    documents.append(hit)
                                    logger.debug(f"✅ Processed direct dict: {hit.get('name', 'Unknown')}")
                                else:
                                    # This doesn't look like a document
                                    logger.warning(f"⚠️ Dict doesn't appear to be a document: keys={list(hit.keys())}")
                                    
                            elif isinstance(hit, (list, tuple)):
                                # Handle nested list/tuple structure
                                logger.debug(f"🔍 Found nested structure: {type(hit)}, len={len(hit)}")
                                # Skip lists and tuples that don't contain document data
                                continue
                                
                            elif isinstance(hit, (int, float)):
                                # Skip numeric values (might be scores)
                                logger.debug(f"🔍 Skipping numeric value: {hit}")
                                continue
                                
                            else:
                                logger.warning(f"⚠️ Unknown hit type: {type(hit)}")
                                logger.debug(f"⚠️ Hit content: {str(hit)[:200]}")
                                
                        except Exception as e:
                            logger.error(f"❌ Error processing hit {i}: {e}")
                            logger.debug(f"❌ Problematic hit: {str(hit)[:200]}")
                            continue
            
            # Date filtering is now handled by submitted_at field in Milvus query
            # No need for client-side diary date filtering since we use submitted_at
            if filters and filters.get("start_date") and filters.get("end_date"):
                logger.info(f"📅 Date filtering handled by submitted_at field in Milvus query")
                logger.info(f"📊 Found {len(documents)} documents within submitted_at date range")
            
            # DISABLED EMERGENCY FALLBACK: Proper date filtering should return empty results when no documents match
            # This ensures accurate date-based search behavior for Malaysian construction documents
            if len(documents) == 0 and filters and (filters.get("start_date") or filters.get("end_date")):
                logger.info("📅 No documents found within the specified date range - this is correct behavior")
                logger.info("💡 Tip: Verify that documents exist in the requested time period")
                # Emergency fallback is disabled to maintain proper date filtering behavior
            
            query_time = time.time() - start_time
            
            # Update cache if enabled
            if use_cache and cache_key:
                self._update_cache(cache_key, (documents, query_time))
            
            # Update metrics
            self._update_metrics(query_time)
            
            logger.info(f"⚡ Milvus search completed: {len(documents)} results in {query_time:.3f}s")
            
            # Log first few document names for debugging
            if documents:
                sample_names = []
                for doc in documents[:3]:  # First 3 documents
                    try:
                        name = doc.get('name', 'Unknown') or 'Unknown'
                        submitted_at = doc.get('submitted_at', 'Unknown date') or 'Unknown date'
                        # Also show extracted diary date for debugging
                        diary_date = extract_diary_date_from_name(name)
                        diary_str = f" [diary: {diary_date.strftime('%Y-%m-%d')}]" if diary_date else " [no diary date]"
                        sample_name = f"{name} (submitted: {submitted_at}){diary_str}"
                        sample_names.append(sample_name)
                    except Exception as e:
                        logger.warning(f"⚠️ Error formatting sample document: {e}")
                        sample_names.append("Error formatting document")
                
                # Filter out None values before joining
                valid_names = [name for name in sample_names if name is not None]
                if valid_names:
                    logger.info(f"📄 Sample documents found: {'; '.join(valid_names[:2])}")  # Limit to 2 for readability
                
                if len(documents) > 2:
                    logger.info(f"📄 Plus {len(documents) - 2} more documents...")
            else:
                logger.warning(f"⚠️ No documents found for project '{project_id}' with filter: {filter_expr}")
            
            return documents, query_time
            
        except Exception as e:
            logger.error(f"❌ Milvus search error: {e}")
            query_time = time.time() - start_time
            self._update_metrics(query_time)
            raise
        finally:
            self._pool.return_connection(client)
    
    def batch_search(
        self,
        embeddings: List[List[float]],
        project_ids: List[str],
        limit: int = 100
    ) -> List[Tuple[List[Dict[str, Any]], float]]:
        """
        Batch search for multiple queries in parallel.
        """
        logger.info(f"🔄 Batch searching {len(embeddings)} queries...")
        
        def search_single(embedding, project_id):
            return self.search_similar(embedding, project_id, limit, use_cache=True)
        
        # Execute searches in parallel
        futures = []
        with self._executor as executor:
            for embedding, project_id in zip(embeddings, project_ids):
                future = executor.submit(search_single, embedding, project_id)
                futures.append(future)
        
        # Collect results
        results = []
        for future in futures:
            try:
                result = future.result(timeout=10)
                results.append(result)
            except Exception as e:
                logger.error(f"Batch search item failed: {e}")
                results.append(([], 0))
        
        logger.info("✅ Batch search completed")
        return results
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics."""
        total_queries = self._metrics["total_queries"]
        cache_hits = self._metrics["cache_hits"]
        
        avg_query_time = 0
        if self._metrics["query_times"]:
            avg_query_time = sum(self._metrics["query_times"]) / len(self._metrics["query_times"])
        
        return {
            "total_queries": total_queries,
            "cache_hits": cache_hits,
            "cache_misses": self._metrics["cache_misses"],
            "cache_hit_ratio": (cache_hits / total_queries) if total_queries > 0 else 0,
            "average_query_time": avg_query_time,
            "cache_size": len(self._query_cache),
            "cache_max_size": self._cache_max_size,
            "connection_pool_size": len(self._pool._connections),
            "uri": self.uri
        }
    
    def clear_cache(self):
        """Clear the query cache."""
        self._query_cache.clear()
        logger.info("🗑️ Query cache cleared")
    
    def warmup(self, sample_embeddings: List[List[float]], project_id: str):
        """Warmup the database with sample queries."""
        logger.info("🔥 Warming up vector database...")
        
        for embedding in sample_embeddings[:3]:
            try:
                self.search_similar(embedding, project_id, limit=10, use_cache=False)
            except:
                pass
        
        logger.info("✅ Vector database warmup complete")
    
    def insert_documents(self, vector_records: List[Dict]) -> bool:
        """
        Insert document vectors into Milvus collection.
        
        Args:
            vector_records: List of records to insert, each containing:
                - id: Document/chunk ID
                - vector: Embedding vector
                - Other metadata fields
                
        Returns:
            True if insertion successful, False otherwise
        """
        try:
            logger.info(f"📥 Inserting {len(vector_records)} documents into Milvus...")
            
            client = self._pool.get_connection()
            
            try:
                # Insert the data
                result = client.insert(
                    collection_name=self.collection_name,
                    data=vector_records
                )
                
                # Flush to ensure data is persisted and available for search immediately
                logger.info("🔄 Flushing collection to make new documents searchable...")
                client.flush(collection_name=self.collection_name)
                logger.info("✅ Collection flushed - new documents should be immediately searchable")
                
                logger.info(f"✅ Successfully inserted {len(vector_records)} documents")
                return True
                
            finally:
                self._pool.return_connection(client)
                
        except Exception as e:
            logger.error(f"❌ Failed to insert documents: {str(e)}")
            return False
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """Get collection statistics."""
        try:
            client = self._pool.get_connection()
            try:
                # Get collection info
                collections = client.list_collections()
                if self.collection_name not in collections:
                    return {
                        "collection_name": self.collection_name,
                        "exists": False,
                        "total_documents": 0,
                        "timestamp": datetime.now().isoformat()
                    }

                # Get collection statistics
                stats = client.get_collection_stats(collection_name=self.collection_name)

                return {
                    "collection_name": self.collection_name,
                    "exists": True,
                    "total_documents": stats.get("row_count", 0),
                    "timestamp": datetime.now().isoformat(),
                    "stats": stats
                }

            finally:
                self._pool.return_connection(client)

        except Exception as e:
            logger.error(f"❌ Error getting collection stats: {str(e)}")
            return {
                "collection_name": self.collection_name,
                "exists": False,
                "total_documents": 0,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    def shutdown(self):
        """Clean shutdown."""
        logger.info("🔚 Shutting down OptimizedVectorDB...")
        self._pool.close_all()
        self._executor.shutdown(wait=True)
        logger.info("✅ Shutdown complete")


# Singleton instance getter
_optimized_db_instance = None
_db_lock = threading.Lock()

def get_optimized_vector_db(collection_name: str = "project_documents") -> OptimizedVectorDB:
    """Get or create the optimized vector database singleton."""
    global _optimized_db_instance
    
    if _optimized_db_instance is None:
        with _db_lock:
            if _optimized_db_instance is None:
                _optimized_db_instance = OptimizedVectorDB(collection_name)
    
    return _optimized_db_instance
