"""
High-performance vector database search tools with performance enhancements.
"""
from datetime import datetime
from typing import Dict, Any, Optional
from agno.tools import tool
import logging
import re

# Import optimized versions (non-optimized versions removed)
logger = logging.getLogger(__name__)

from ..hooks.tool_hooks import standard_pre_hook, standard_post_hook

# Import Logfire for monitoring
try:
    from utils.logfire_config import log_vector_search, log_error
except ImportError:
    # Fallback if Logfire not available
    def log_vector_search(*args, **kwargs):
        pass
    def log_error(*args, **kwargs):
        pass

try:
    from .services.embedding_service_optimized import get_optimized_embedding_service
    from .milvus_client_optimized import get_optimized_vector_db
    USE_OPTIMIZED = True
except ImportError:
    USE_OPTIMIZED = False
    logger.warning("⚠️ Optimized services not available, using standard versions")

def _extract_essential_site_diary_content(content_text: str) -> Dict[str, Any]:
    """
    Extract only the essential data needed for site diary analysis.
    This dramatically reduces payload size by extracting structured data instead of raw text.

    Args:
        content_text: Raw content text from site diary document

    Returns:
        Dictionary with extracted essential data for analysis
    """
    try:
        if not content_text or not isinstance(content_text, str):
            return {'error': 'No content available'}

        # Extract key sections using regex patterns
        extracted_data = {
            'dates': [],
            'worker_counts': [],
            'materials': [],
            'equipment': [],
            'weather': [],
            'activities': [],
            'delays': [],
            'content_summary': ''
        }

        # Extract TTARIKH (dates)
        date_patterns = [
            r'TTARIKH\s*:?\s*(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{4})',
            r'TARIKH\s*:?\s*(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{4})',
            r'Date\s*:?\s*(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{4})'
        ]
        for pattern in date_patterns:
            dates = re.findall(pattern, content_text, re.IGNORECASE)
            extracted_data['dates'].extend(dates)

        # Extract BILANGAN PEKERJA (worker counts)
        worker_patterns = [
            r'BILANGAN PEKERJA.*?(\d+)',
            r'Jumlah.*?(\d+)',
            r'Total.*?(\d+)',
            r'Worker.*?(\d+)'
        ]
        for pattern in worker_patterns:
            workers = re.findall(pattern, content_text, re.IGNORECASE | re.DOTALL)
            extracted_data['worker_counts'].extend(workers)

        # Extract CUACA (weather)
        weather_patterns = [
            r'CUACA\s*:?\s*([A-Za-z\s]+)',
            r'Weather\s*:?\s*([A-Za-z\s]+)'
        ]
        for pattern in weather_patterns:
            weather = re.findall(pattern, content_text, re.IGNORECASE)
            extracted_data['weather'].extend([w.strip() for w in weather if w.strip()])

        # Extract materials (BAHAN-BAHAN)
        material_patterns = [
            r'BAHAN-BAHAN.*?(\d+.*?(?:bag|m3|Load|unit))',
            r'Material.*?(\d+.*?(?:bag|m3|Load|unit))'
        ]
        for pattern in material_patterns:
            materials = re.findall(pattern, content_text, re.IGNORECASE | re.DOTALL)
            extracted_data['materials'].extend(materials)

        # Extract equipment (PERALATAN/LOJI)
        equipment_patterns = [
            r'PERALATAN.*?([A-Za-z0-9\s\-]+)',
            r'LOJI.*?([A-Za-z0-9\s\-]+)',
            r'Equipment.*?([A-Za-z0-9\s\-]+)'
        ]
        for pattern in equipment_patterns:
            equipment = re.findall(pattern, content_text, re.IGNORECASE | re.DOTALL)
            extracted_data['equipment'].extend([e.strip() for e in equipment if e.strip() and len(e.strip()) > 2])

        # Create a concise summary (first 300 chars of meaningful content)
        # Remove excessive whitespace and formatting
        clean_content = re.sub(r'\s+', ' ', content_text)
        clean_content = re.sub(r'[#*]+', '', clean_content)
        extracted_data['content_summary'] = clean_content[:300] + '...' if len(clean_content) > 300 else clean_content

        return extracted_data

    except Exception as e:
        logger.warning(f"Error extracting essential content: {e}")
        return {'error': f'Content extraction failed: {str(e)}'}


def _generate_clean_content_preview(content_text: str, max_length: int = 200) -> str:
    """
    Generate a clean content preview that filters out analysis recommendations
    and ensures only document content is included.

    Args:
        content_text: Raw content text from document
        max_length: Maximum length of preview (default: 200)

    Returns:
        Clean content preview containing only document content
    """
    try:
        if not content_text or not isinstance(content_text, str):
            return ''

        # Patterns that indicate analysis content (not document content)
        analysis_patterns = [
            r'^\d+\.\s*(Immediate Actions|Short-Term Actions|Long-Term Actions)',
            r'^\d+\.\s*Prioritized Recommendations',
            r'^\s*###?\s*(Immediate Actions|Short-Term|Long-Term)',
            r'^\s*##\s*\d+\.\s*(Executive Summary|Document Overview|Key Insights)',
            r'^\s*\*\*?(Immediate|Short-Term|Long-Term)\s*Actions',
            r'based on current progress',
            r'representing \d+% of scheduled time',
            r'threatens.*milestone by \d+ days'
        ]
        
        # Split content into lines for analysis
        lines = content_text.split('\n')
        clean_lines = []
        
        # Filter out analysis content line by line
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # Skip lines that match analysis patterns
            is_analysis = False
            for pattern in analysis_patterns:
                if re.match(pattern, line, re.IGNORECASE):
                    is_analysis = True
                    break
            
            if not is_analysis:
                clean_lines.append(line)
                
            # Stop if we have enough content for preview
            clean_content = ' '.join(clean_lines)
            if len(clean_content) >= max_length:
                break
        
        # Join the clean lines
        clean_content = ' '.join(clean_lines)
        
        # Truncate to max length if needed
        if len(clean_content) > max_length:
            clean_content = clean_content[:max_length] + '...'
        
        return clean_content
        
    except Exception as e:
        logger.warning(f"Error generating clean content preview: {e}")
        # Fallback to simple truncation
        return (content_text[:max_length] + '...') if len(content_text) > max_length else content_text

@tool(
    show_result=False,
    pre_hook=standard_pre_hook,
    post_hook=standard_post_hook
)
def search_documents_vector(
    query_text: str,
    project_id: str,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    document_type: Optional[str] = None,
    workspace_group: Optional[str] = None,
    limit: int = 50,  # Reduced from 100 for performance optimization
    include_content: bool = False  # New parameter to control content_text inclusion
) -> Dict[str, Any]:
    """High-performance vector search with caching and performance enhancements.
    
    This tool searches for documents using semantic vector similarity, filtered by project and optional criteria like date ranges and document types.
    
    Args:
        query_text: Natural language query or keywords to search for (e.g., 'site diary', 'safety reports')
        project_id: Project ID for filtering documents (REQUIRED - must be provided)
        start_date: Start date in ISO format (e.g., '2025-08-01T00:00:00Z') - optional
        end_date: End date in ISO format (e.g., '2025-08-31T23:59:59Z') - optional
        document_type: Type of document to filter by (e.g., 'site_diary', 'safety_report') - optional
        workspace_group: Workspace group name to filter by - optional
        limit: Maximum number of results to return (default: 50, optimized for performance)
        include_content: Include full document content_text in response (default: false for lightweight responses). Set to true only when content analysis is needed.
    
    Returns:
        Dictionary with search results and execution metadata
    """
    start_time = datetime.now()
    
    # CRITICAL: Validate project_id is provided
    if not project_id or project_id.strip() == "":
        logger.error("❌ CRITICAL ERROR: project_id is required but was empty or None")
        return {
            'success': False,
            'error': 'Project ID is required for document searches. Please provide a valid project_id.',
            'error_type': 'missing_project_id',
            'total_count': 0,
            'documents': [],
            'execution_time': 0,
            'validation_failed': True
        }
    
    # CRITICAL: Site diary validation check - system-level enforcement
    query_lower = query_text.lower().strip()
    is_site_diary_query = any(keyword in query_lower for keyword in ['site diary', 'diary', 'diaries'])
    
    if is_site_diary_query and not start_date and not end_date:
        logger.warning("Site diary search blocked - date range required")
        return {
            'success': False,
            'error': 'To search site diaries effectively, please specify a date range such as "last week", "August 2025", or "2025-08-20 to 2025-08-25".',
            'error_type': 'date_required_for_site_diary',
            'total_count': 0,
            'documents': [],
            'execution_time': 0,
            'system_blocked': True,
            'suggestions': [
                'Site diary from last week',
                'Site diary for August 2025', 
                'Site diary from 2025-08-20 to 2025-08-25',
                'Recent site diary entries'
            ]
        }
    
    # Log search parameters for debugging
    logger.info("🔍 Vector search initiated with parameters:")
    logger.info(f"   - project_id: '{project_id}'")
    logger.info(f"   - query_text: '{query_text}'")
    logger.info(f"   - date_range: {start_date} to {end_date}")
    logger.info(f"   - document_type: {document_type}")
    logger.info(f"   - workspace_group: {workspace_group}")
    
    # Validate input parameters
    if not query_text or not isinstance(query_text, str):
        return {
            'success': False,
            'error': f'Invalid query text: {type(query_text)} - {repr(query_text)}',
            'error_type': 'invalid_input',
            'total_count': 0,
            'documents': [],
            'execution_time': (datetime.now() - start_time).total_seconds(),
            'performance_notes': 'Invalid query text provided'
        }
    
    if not project_id or not isinstance(project_id, str):
        return {
            'success': False,
            'error': f'Invalid project_id: {type(project_id)} - {repr(project_id)}',
            'error_type': 'invalid_input',
            'total_count': 0,
            'documents': [],
            'execution_time': (datetime.now() - start_time).total_seconds(),
            'performance_notes': 'Invalid project_id provided'
        }
    
    try:
        # Use optimized services if available
        if USE_OPTIMIZED:
            logger.info("⚡ Using optimized services for vector search")
            embedding_service = get_optimized_embedding_service()
            vector_db = get_optimized_vector_db()
        else:
            raise ImportError("Optimized services required - non-optimized versions removed")
        
        # Generate embedding with caching
        query_embedding = embedding_service.generate_embedding(query_text)
        
        if not query_embedding:
            return {
                'success': False,
                'error': 'Failed to generate query embedding',
                'error_type': 'embedding_error',
                'total_count': 0,
                'documents': [],
                'execution_time': (datetime.now() - start_time).total_seconds(),
                'performance_notes': 'Embedding generation failed'
            }
        
        embedding_time = (datetime.now() - start_time).total_seconds()
        
        # Perform search
        try:
            documents = vector_db.search_documents(
                query_embedding=query_embedding,
                project_id=project_id,
                document_type=document_type,
                start_date=start_date,
                end_date=end_date,
                workspace_group=workspace_group,
                limit=limit
            )
            logger.info(f"🔍 Raw search returned {len(documents)} documents of type: {type(documents)}")
            if documents and len(documents) > 0:
                # Log first document structure for debugging
                first_doc = documents[0]
                logger.info(f"🔍 First document structure: {type(first_doc)} - Keys: {list(first_doc.keys()) if isinstance(first_doc, dict) else 'Not a dict'}")
                if isinstance(first_doc, dict):
                    logger.info(f"🔍 First document sample data:")
                    for key in ['document_id', 'name', 'file_url', 'category', 'content_text']:
                        value = first_doc.get(key)
                        logger.info(f"   {key}: {type(value)} = {repr(value)[:100] if value else None}")
            
        except Exception as search_error:
            logger.error(f"❌ Search operation failed: {search_error}")
            raise
        
        search_time = (datetime.now() - start_time).total_seconds() - embedding_time
        total_time = (datetime.now() - start_time).total_seconds()
        total_count = len(documents)
        
        # Format documents (optimized with type safety)
        formatted_docs = []
        for i, doc in enumerate(documents):
            try:
                # Type safety check: ensure doc is a dictionary
                if not isinstance(doc, dict):
                    logger.warning(f"⚠️ Skipping non-dictionary document at index {i}: {type(doc)} - {doc}")
                    continue
                    
                # Enhanced content processing with mixed content detection
                content_text = doc.get('content_text') or ''
                if not isinstance(content_text, str):
                    content_text = str(content_text) if content_text is not None else ''
                
                # Generate clean content preview with validation
                content_preview = _generate_clean_content_preview(content_text)
                
                # Extract date from filename for comparison
                from tools.vector_db.milvus_client_optimized import extract_diary_date_from_name
                doc_name = doc.get('name', '')
                filename_date = extract_diary_date_from_name(doc_name)
                
                # Log date comparison for debugging inconsistencies
                submitted_at = doc.get('submitted_at', '')
                created_at = doc.get('created_at', '')
                
                logger.info(f"📅 DATE DEBUG for {doc_name}:")
                logger.info(f"   - Filename date: {filename_date.strftime('%Y-%m-%d') if filename_date else 'None'}")
                logger.info(f"   - Database submitted_at: {submitted_at}")
                logger.info(f"   - Database created_at: {created_at}")
                
                # Add extracted date to results for agent consistency
                extracted_date = filename_date.strftime('%Y-%m-%d') if filename_date else ''
                
                # More selective None handling - only convert None to avoid join errors, preserve actual values
                formatted_doc = {
                    'id': doc.get('document_id') if doc.get('document_id') is not None else '',
                    'name': doc.get('name') if doc.get('name') is not None else 'Unknown Document',  
                    'fileUrl': doc.get('file_url') if doc.get('file_url') is not None else '',
                    'category': doc.get('category') if doc.get('category') is not None else '',
                    'addedBy': doc.get('added_by') if doc.get('added_by') is not None else '',
                    'submittedAt': doc.get('submitted_at') if doc.get('submitted_at') is not None else '',
                    'createdAt': doc.get('created_at') if doc.get('created_at') is not None else '',
                    'extractedDate': extracted_date,  # Add filename-extracted date for consistency
                    'workspaceGroup': {
                        'name': doc.get('workspace_group') if doc.get('workspace_group') is not None else ''
                    },
                    'similarityScore': doc.get('similarity_score', 0.0),
                    'contentPreview': content_preview
                }
                
                # Smart content inclusion for analysis agents
                if include_content:
                    # For site diaries, extract essential structured data instead of raw text
                    if doc.get('category') == 'site_diary' or 'site diary' in doc.get('name', '').lower():
                        # Use smart extraction to reduce payload size dramatically
                        essential_data = _extract_essential_site_diary_content(content_text)
                        formatted_doc['essential_data'] = essential_data
                        # Include a small content sample for context
                        formatted_doc['content_sample'] = content_text[:500] + '...' if len(content_text) > 500 else content_text
                    else:
                        # For non-site-diary documents, include full content
                        formatted_doc['content_text'] = content_text
                formatted_docs.append(formatted_doc)
                
            except Exception as e:
                logger.error(f"❌ Error formatting document at index {i}: {e}")
                logger.debug(f"❌ Problematic document: {doc}")
                continue
        
        # Performance classification
        performance_rating = "fast" if total_time < 1.0 else "medium" if total_time < 3.0 else "slow"
        
        # Enhanced logging with project context
        logger.info(f"🚀 Vector search completed for project '{project_id}': {total_count} docs in {total_time:.3f}s ({performance_rating})")
        
        # Log to Logfire for monitoring
        log_vector_search(
            project_id=project_id,
            query=query_text,
            result_count=total_count,
            execution_time=total_time,
            success=True
        )
        
        # Log document names for debugging consistency issues
        try:
            if formatted_docs:
                # Ensure no None values in doc names for logging - ultra-safe version
                doc_names = []
                for doc in formatted_docs[:5]:  # First 5
                    if isinstance(doc, dict):
                        try:
                            name = doc.get('name')
                            if name is None:
                                name = 'Unknown'
                            elif not isinstance(name, str):
                                name = str(name)
                            doc_names.append(name)
                        except Exception as name_error:
                            logger.debug(f"Error extracting doc name: {name_error}")
                            doc_names.append('Error')
                
                # Final safety check before join
                safe_names = [str(name) for name in doc_names if name is not None]
                if safe_names:
                    logger.info(f"📄 Found documents: {', '.join(safe_names)}")
                
                if len(formatted_docs) > 5:
                    logger.info(f"📄 ... and {len(formatted_docs) - 5} more documents")
            else:
                logger.warning(f"⚠️  No documents found for project '{project_id}' with current filters")
        except Exception as log_error:
            logger.debug(f"⚠️ Error in document name logging: {log_error}")
            logger.info(f"📄 Found {len(formatted_docs)} documents (names not displayable)")
        
        return {
            'success': True,
            'total_count': total_count,
            'documents': formatted_docs,
            'date_range': {
                'start_date': start_date or '',
                'end_date': end_date or ''
            },
            'project_id': project_id or '',
            'execution_time': total_time,
            'search_query': query_text or '',
            'document_type': document_type or '',
            'search_method': 'vector_similarity',
            'performance_metrics': {
                'embedding_time': embedding_time,
                'search_time': search_time,
                'total_time': total_time,
                'performance_rating': performance_rating,
                'documents_per_second': total_count / total_time if total_time > 0 else 0
            },
            'debug_info': {
                'project_id_used': project_id or '',
                'filters_applied': {
                    'project_id': project_id or '',
                    'start_date': start_date or '',
                    'end_date': end_date or '',
                    'document_type': document_type or '',
                    'workspace_group': workspace_group or ''
                },
                'search_consistent': True
            }
        }
        
    except Exception as e:
        total_time = (datetime.now() - start_time).total_seconds()
        error_msg = f'Vector search failed for project {project_id}: {str(e)}'
        
        logger.error(f"❌ {error_msg} (after {total_time:.3f}s)")
        logger.error(f"❌ Search context - project_id: '{project_id}', query: '{query_text}'")
        
        # Log error to Logfire
        log_error(e, "vector_search", 
                 project_id=project_id, 
                 query=query_text, 
                 execution_time=total_time)
        
        # Log failed search to Logfire
        log_vector_search(
            project_id=project_id,
            query=query_text,
            result_count=0,
            execution_time=total_time,
            success=False
        )
        
        return {
            'success': False,
            'error': error_msg,
            'error_type': 'vector_search_error',
            'total_count': 0,
            'documents': [],
            'execution_time': total_time,
            'suggestion': 'Check vector database connection and embedding service',
            'project_id': project_id,
            'debug_info': {
                'project_id_used': project_id,
                'error_context': str(e),
                'search_consistent': False
            },
            'performance_metrics': {
                'total_time': total_time,
                'performance_rating': 'failed'
            }
        }


@tool(
    pre_hook=standard_pre_hook,
    post_hook=standard_post_hook,
    show_result=False
)
def get_vector_performance_stats() -> Dict[str, Any]:
    """Get performance statistics for the vector search system including health status and metrics.
    
    Returns:
        Dictionary with performance metrics and cache statistics
    """
    try:
        # Get embedding service stats
        embedding_service = get_optimized_embedding_service()
        embedding_stats = embedding_service.get_cache_stats()
        
        # Get vector database stats  
        vector_db = get_optimized_vector_db()
        db_stats = vector_db.get_performance_stats()
        
        return {
            'success': True,
            'embedding_service': {
                'cache_stats': embedding_stats,
                'service_type': 'EmbeddingService'
            },
            'vector_database': {
                'stats': db_stats,
                'client_type': 'OptimizedVectorDB'
            },
            'system_status': 'healthy',
            'timestamp': datetime.now().isoformat()
        }
        
    except Exception as e:
        return {
            'success': False,
            'error': f'Failed to get performance stats: {str(e)}',
            'system_status': 'error',
            'timestamp': datetime.now().isoformat()
        }


@tool(
    pre_hook=standard_pre_hook,
    post_hook=standard_post_hook,
    show_result=False
)
def search_similar_documents(
    document_id: str,
    project_id: str,
    limit: int = 10
) -> Dict[str, Any]:
    """Find documents similar to a given document using vector similarity search.
    
    Args:
        document_id: ID of the reference document to find similar documents for
        project_id: Project ID for filtering documents (REQUIRED)
        limit: Maximum number of similar documents to return (default: 10)
    
    Returns:
        Dictionary with similar documents and performance metrics
    """
    start_time = datetime.now()
    
    try:
        # Use optimized services
        embedding_service = get_optimized_embedding_service()
        
        # Generate embedding for similarity search
        # Note: In a full implementation, you'd retrieve the actual document content
        # For now, use a generic similarity query
        query_embedding = embedding_service.generate_embedding(f"document similar to {document_id}")
        
        if not query_embedding:
            return {
                'success': False,
                'error': 'Failed to generate similarity embedding',
                'total_count': 0,
                'documents': [],
                'execution_time': (datetime.now() - start_time).total_seconds()
            }
        
        vector_db = get_optimized_vector_db()
        documents = vector_db.search_documents(
            query_embedding=query_embedding,
            project_id=project_id,
            limit=limit + 1  # +1 to account for filtering out original
        )
        
        # Filter out the original document with type safety
        similar_docs = [doc for doc in documents if isinstance(doc, dict) and doc.get('document_id') != document_id][:limit]
        
        execution_time = (datetime.now() - start_time).total_seconds()
        
        logger.info(f"🔗 Similarity search: {len(similar_docs)} docs in {execution_time:.3f}s")
        
        return {
            'success': True,
            'reference_document_id': document_id,
            'total_count': len(similar_docs),
            'documents': similar_docs,
            'project_id': project_id,
            'execution_time': execution_time,
            'search_method': 'similarity',
            'performance_rating': 'fast' if execution_time < 1.0 else 'medium' if execution_time < 3.0 else 'slow'
        }
        
    except Exception as e:
        execution_time = (datetime.now() - start_time).total_seconds()
        return {
            'success': False,
            'error': f'Similarity search failed: {str(e)}',
            'error_type': 'similarity_search_error',
            'total_count': 0,
            'documents': [],
            'execution_time': execution_time
        }


@tool(
    show_result=False,
    pre_hook=standard_pre_hook,
    post_hook=standard_post_hook
)
def search_site_diaries_optimized(
    project_id: str,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    limit: int = 10
) -> Dict[str, Any]:
    """
    Optimized search specifically for site diary analysis.
    Returns only essential structured data to prevent API timeouts.

    Args:
        project_id: Project ID for filtering documents (REQUIRED)
        start_date: Start date in ISO format (optional)
        end_date: End date in ISO format (optional)
        limit: Maximum number of results (default: 10, optimized for performance)

    Returns:
        Dictionary with essential site diary data for analysis
    """
    try:
        logger.info(f"🔍 Optimized site diary search for project '{project_id}'")

        # Use the main search function but with optimized parameters
        result = search_documents_vector(
            query_text="site diary",
            project_id=project_id,
            start_date=start_date,
            end_date=end_date,
            document_type="site_diary",
            limit=limit,
            include_content=True  # Will use smart extraction
        )

        if result.get('success'):
            # Further optimize by ensuring we only return essential data
            optimized_docs = []
            for doc in result.get('documents', []):
                if 'essential_data' in doc:
                    # Site diary with extracted data
                    optimized_doc = {
                        'id': doc.get('id'),
                        'name': doc.get('name'),
                        'date_extracted': doc['essential_data'].get('dates', []),
                        'worker_counts': doc['essential_data'].get('worker_counts', []),
                        'materials': doc['essential_data'].get('materials', []),
                        'equipment': doc['essential_data'].get('equipment', []),
                        'weather': doc['essential_data'].get('weather', []),
                        'activities': doc['essential_data'].get('activities', []),
                        'delays': doc['essential_data'].get('delays', []),
                        'summary': doc['essential_data'].get('content_summary', ''),
                        'category': doc.get('category'),
                        'submitted_at': doc.get('submittedAt')
                    }
                    optimized_docs.append(optimized_doc)

            result['documents'] = optimized_docs
            result['search_method'] = 'optimized_site_diary_analysis'

            logger.info(f"✅ Optimized search completed: {len(optimized_docs)} site diaries processed")

        return result

    except Exception as e:
        error_msg = f"Optimized site diary search failed: {str(e)}"
        logger.error(f"❌ {error_msg}")

        return {
            "success": False,
            "error": error_msg,
            "total_count": 0,
            "documents": [],
            "execution_time": 0.0,
            "search_method": "optimized_site_diary_analysis",
            "project_id": project_id
        }
