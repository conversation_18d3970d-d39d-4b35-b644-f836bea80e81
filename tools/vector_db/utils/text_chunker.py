"""
Text chunking utilities for splitting documents into manageable pieces.
"""
import re
import tiktoken
from typing import List, Optional
from ..models.vectorization import DocumentChunk
import logging

logger = logging.getLogger(__name__)


class TextChunker:
    """Text chunker for splitting documents into semantic chunks."""
    
    def __init__(self):
        """Initialize text chunker with tiktoken encoder."""
        try:
            # Use the same encoding as the embedding models
            self.encoding = tiktoken.get_encoding("cl100k_base")
        except Exception as e:
            logger.warning(f"Failed to initialize tiktoken encoder: {str(e)}")
            self.encoding = None
    
    def count_tokens(self, text: str) -> int:
        """
        Count actual tokens in text using tiktoken.
        
        Args:
            text: Input text
            
        Returns:
            Actual token count
        """
        if self.encoding is None:
            # Fallback to character-based estimation
            return len(text) // 4
        
        try:
            return len(self.encoding.encode(text))
        except Exception as e:
            logger.warning(f"Error counting tokens: {str(e)}")
            # Fallback to character-based estimation
            return len(text) // 4
    
    @staticmethod
    def estimate_token_count(text: str) -> int:
        """
        Estimate token count for text (legacy method for backward compatibility).
        Rough approximation: 1 token ≈ 4 characters
        
        Args:
            text: Input text
            
        Returns:
            Estimated token count
        """
        return len(text) // 4
    
    @staticmethod
    def clean_text(text: str) -> str:
        """
        Clean and normalize text content.
        
        Args:
            text: Raw text content
            
        Returns:
            Cleaned text
        """
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove control characters but keep newlines and tabs
        text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)
        
        # Normalize quotes and dashes
        text = text.replace('"', '"').replace('"', '"')
        text = text.replace(''', "'").replace(''', "'")
        text = text.replace('–', '-').replace('—', '-')
        
        return text.strip()
    
    @classmethod
    def chunk_by_sentences(
        cls, 
        text: str, 
        max_chunk_size: int = 1000, 
        overlap: int = 200,
        document_id: str = "unknown"
    ) -> List[DocumentChunk]:
        """
        Split text into chunks by sentences with overlap.
        
        Args:
            text: Input text to chunk
            max_chunk_size: Maximum characters per chunk
            overlap: Number of characters to overlap between chunks
            document_id: Document identifier
            
        Returns:
            List of DocumentChunk objects
        """
        # Clean text first
        text = cls.clean_text(text)
        
        if not text:
            logger.warning("Empty text provided for chunking")
            return []
        
        # Split into sentences
        sentence_endings = r'[.!?]+(?:\s+|$)'
        sentences = re.split(f'({sentence_endings})', text)
        
        # Reconstruct sentences with their punctuation
        reconstructed_sentences = []
        for i in range(0, len(sentences) - 1, 2):
            if i + 1 < len(sentences):
                sentence = sentences[i] + (sentences[i + 1] if sentences[i + 1].strip() else '')
                if sentence.strip():
                    reconstructed_sentences.append(sentence.strip())
        
        if not reconstructed_sentences:
            # Fallback: use simple splitting if sentence detection fails
            logger.warning("Sentence detection failed, using simple word splitting")
            return cls.chunk_by_words(text, max_chunk_size, overlap, document_id)
        
        # Build chunks from sentences
        chunks = []
        current_chunk = []
        current_size = 0
        
        for sentence in reconstructed_sentences:
            sentence_size = len(sentence)
            
            # If single sentence is too large, split it further
            if sentence_size > max_chunk_size:
                # Finish current chunk if it has content
                if current_chunk:
                    chunk_text = ' '.join(current_chunk)
                    chunks.append(chunk_text)
                    current_chunk = []
                    current_size = 0
                
                # Split large sentence into smaller parts
                large_sentence_chunks = cls.chunk_by_words(
                    sentence, max_chunk_size, overlap, document_id
                )
                chunks.extend([chunk.content for chunk in large_sentence_chunks])
                continue
            
            # Check if adding this sentence would exceed limit
            if current_size + sentence_size > max_chunk_size and current_chunk:
                # Save current chunk
                chunk_text = ' '.join(current_chunk)
                chunks.append(chunk_text)
                
                # Start new chunk with overlap
                if overlap > 0 and chunks:
                    overlap_text = chunk_text[-overlap:]
                    current_chunk = [overlap_text, sentence]
                    current_size = len(overlap_text) + sentence_size
                else:
                    current_chunk = [sentence]
                    current_size = sentence_size
            else:
                current_chunk.append(sentence)
                current_size += sentence_size
        
        # Add final chunk if it has content
        if current_chunk:
            chunk_text = ' '.join(current_chunk)
            chunks.append(chunk_text)
        
        # Convert to DocumentChunk objects
        document_chunks = []
        chunker_instance = cls()  # Create instance to access token counting
        for i, chunk_text in enumerate(chunks):
            chunk = DocumentChunk(
                chunk_id=f"{document_id}_chunk_{i:03d}",
                content=chunk_text,
                chunk_index=i,
                page_number=cls._extract_page_number(chunk_text),
                token_count=chunker_instance.count_tokens(chunk_text)  # Use accurate token counting
            )
            document_chunks.append(chunk)
        
        logger.info(f"Created {len(document_chunks)} chunks from {len(text)} characters")
        return document_chunks

    @classmethod
    def chunk_by_markdown_sections(
        cls,
        text: str,
        max_chunk_size: int = 2000,
        overlap: int = 150,
        document_id: str = "unknown"
    ) -> List[DocumentChunk]:
        """
        Split text into chunks by Markdown sections, preserving structure.

        Args:
            text: Input Markdown text to chunk
            max_chunk_size: Maximum characters per chunk
            overlap: Number of characters to overlap between chunks
            document_id: Document identifier

        Returns:
            List of DocumentChunk objects with preserved Markdown structure
        """
        # Clean text first
        text = cls.clean_text(text)

        if not text:
            logger.warning("Empty text provided for Markdown chunking")
            return []

        # Split by major section boundaries (pages and main headers)
        sections = cls._split_by_markdown_sections(text)

        document_chunks = []
        chunk_index = 0

        for section in sections:
            if not section.strip():
                continue

            # If section is small enough, keep it as one chunk
            if len(section) <= max_chunk_size:
                chunk = DocumentChunk(
                    chunk_id=f"{document_id}_chunk_{chunk_index}",
                    content=section.strip(),
                    chunk_index=chunk_index,
                    page_number=cls._extract_page_number(section),
                    token_count=cls.estimate_token_count(section)
                )
                document_chunks.append(chunk)
                chunk_index += 1
            else:
                # Split large sections while preserving subsections
                subsection_chunks = cls._split_large_markdown_section(
                    section, max_chunk_size, overlap, document_id, chunk_index
                )
                document_chunks.extend(subsection_chunks)
                chunk_index += len(subsection_chunks)

        logger.info(f"Created {len(document_chunks)} Markdown-aware chunks from {len(text)} characters")
        return document_chunks

    @classmethod
    def _split_by_markdown_sections(cls, text: str) -> List[str]:
        """Split text by major Markdown section boundaries."""
        import re

        sections = []
        current_section = ""
        lines = text.split('\n')

        for line in lines:
            # Check if this line starts a new major section
            is_section_boundary = (
                line.startswith('---') or  # Page separators
                line.startswith('# Page') or  # Page headers
                line.startswith('### ') or  # Level 3 headers (table sections)
                line.startswith('## ')  # Level 2 headers (major sections)
            )

            if is_section_boundary and current_section.strip():
                # Save current section and start new one
                sections.append(current_section.strip())
                current_section = line + '\n'
            else:
                current_section += line + '\n'

        # Add the last section
        if current_section.strip():
            sections.append(current_section.strip())

        return sections

    @classmethod
    def _split_large_markdown_section(
        cls,
        section: str,
        max_chunk_size: int,
        overlap: int,
        document_id: str,
        start_index: int
    ) -> List[DocumentChunk]:
        """Split a large Markdown section while preserving structure."""
        chunks = []

        # Try to split by subsections first (tables, lists, etc.)
        subsections = cls._split_by_markdown_subsections(section)

        current_chunk = ""
        chunk_index = start_index

        for subsection in subsections:
            # If adding this subsection would exceed max size, finalize current chunk
            if current_chunk and len(current_chunk + subsection) > max_chunk_size:
                chunk = DocumentChunk(
                    chunk_id=f"{document_id}_chunk_{chunk_index}",
                    content=current_chunk.strip(),
                    chunk_index=chunk_index,
                    page_number=cls._extract_page_number(current_chunk),
                    token_count=cls.estimate_token_count(current_chunk)
                )
                chunks.append(chunk)
                chunk_index += 1

                # Start new chunk with overlap
                if overlap > 0 and len(current_chunk) > overlap:
                    overlap_text = current_chunk[-overlap:]
                    current_chunk = overlap_text + "\n\n" + subsection
                else:
                    current_chunk = subsection
            else:
                current_chunk += "\n\n" + subsection if current_chunk else subsection

        # Add the final chunk
        if current_chunk.strip():
            chunk = DocumentChunk(
                chunk_id=f"{document_id}_chunk_{chunk_index}",
                content=current_chunk.strip(),
                chunk_index=chunk_index,
                page_number=cls._extract_page_number(current_chunk),
                token_count=cls.estimate_token_count(current_chunk)
            )
            chunks.append(chunk)

        return chunks

    @classmethod
    def _split_by_markdown_subsections(cls, text: str) -> List[str]:
        """Split text by Markdown subsections (tables, lists, paragraphs)."""
        import re

        # Split by table boundaries, structured content, etc.
        subsection_patterns = [
            r'\n\*\*[^*]+\*\*:',   # Metadata sections like **Key Numbers:**
            r'\n\| .+ \|',         # Table rows
            r'\n- .+',             # List items
        ]

        # For now, split by double newlines to preserve paragraph structure
        subsections = re.split(r'\n\n+', text)
        return [s.strip() for s in subsections if s.strip()]

    @classmethod
    def _extract_page_number(cls, text: str) -> Optional[int]:
        """Extract page number from Markdown text."""
        import re
        match = re.search(r'# Page (\d+)', text)
        return int(match.group(1)) if match else None
    
    @classmethod
    def chunk_by_words(
        cls, 
        text: str, 
        max_chunk_size: int = 1000, 
        overlap: int = 200,
        document_id: str = "unknown"
    ) -> List[DocumentChunk]:
        """
        Split text into chunks by words (fallback method).
        
        Args:
            text: Input text to chunk
            max_chunk_size: Maximum characters per chunk
            overlap: Number of characters to overlap between chunks
            document_id: Document identifier
            
        Returns:
            List of DocumentChunk objects
        """
        text = cls.clean_text(text)
        words = text.split()
        
        if not words:
            return []
        
        chunks = []
        current_chunk = []
        current_size = 0
        
        for word in words:
            word_size = len(word) + 1  # +1 for space
            
            if current_size + word_size > max_chunk_size and current_chunk:
                # Save current chunk
                chunk_text = ' '.join(current_chunk)
                chunks.append(chunk_text)
                
                # Start new chunk with overlap
                if overlap > 0:
                    overlap_words = []
                    overlap_size = 0
                    for w in reversed(current_chunk):
                        if overlap_size + len(w) + 1 <= overlap:
                            overlap_words.insert(0, w)
                            overlap_size += len(w) + 1
                        else:
                            break
                    current_chunk = overlap_words + [word]
                    current_size = sum(len(w) + 1 for w in current_chunk)
                else:
                    current_chunk = [word]
                    current_size = word_size
            else:
                current_chunk.append(word)
                current_size += word_size
        
        # Add final chunk
        if current_chunk:
            chunk_text = ' '.join(current_chunk)
            chunks.append(chunk_text)
        
        # Convert to DocumentChunk objects
        document_chunks = []
        chunker_instance = cls()  # Create instance to access token counting
        for i, chunk_text in enumerate(chunks):
            chunk = DocumentChunk(
                chunk_id=f"{document_id}_chunk_{i:03d}",
                content=chunk_text,
                chunk_index=i,
                page_number=cls._extract_page_number(chunk_text),
                token_count=chunker_instance.count_tokens(chunk_text)  # Use accurate token counting
            )
            document_chunks.append(chunk)
        
        return document_chunks
    
    @staticmethod
    def _extract_page_number(text: str) -> int:
        """
        Extract page number from chunk text if present.
        
        Args:
            text: Chunk text
            
        Returns:
            Page number or None
        """
        # Look for [Page X] pattern at the beginning
        match = re.search(r'^\[Page (\d+)\]', text)
        if match:
            return int(match.group(1))
        return None