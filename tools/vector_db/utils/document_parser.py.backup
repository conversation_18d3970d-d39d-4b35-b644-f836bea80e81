"""
Document parsing utilities using Docling for advanced document processing.
Supports OCR, multiple formats, and AI-optimized text extraction.
"""
import io
import requests
from typing import Tu<PERSON>, Dict, Any, Union, List
from urllib.parse import urlparse
from dataclasses import dataclass, field
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

# Import Docling components
from docling.document_converter import DocumentConverter
from docling.datamodel.base_models import InputFormat
from docling.datamodel.pipeline_options import PdfPipelineOptions

logger.info("✅ Docling successfully imported")


class DocumentParsingError(Exception):
    """Exception raised when document parsing fails."""
    pass


@dataclass
class DoclingConfig:
    """Configuration for Docling document processing."""
    
    # OCR Configuration
    enable_ocr: bool = False  # Changed to False to disable OCR
    ocr_engine: str = "easyocr"  # easyocr, tesseract, rapidocr
    
    # Processing Options
    enable_table_structure: bool = False  # Changed to False to disable table structure
    enable_layout_analysis: bool = False  # Changed to False to disable layout analysis
    max_file_size_mb: int = 10  # Changed to 10 to limit file size
    timeout_seconds: int = 30  # Changed to 30 to reduce timeout
    
    # Supported formats (PDF focus as requested)
    supported_formats: List[str] = field(default_factory=lambda: [
        'pdf', 'docx', 'pptx', 'xlsx', 'html', 'png', 'jpg', 'jpeg', 'tiff'
    ])
    
    # Export preferences
    export_format: str = "markdown"  # markdown, html, json
    include_metadata: bool = True


@dataclass 
class ParsedDocument:
    """Enhanced document representation from Docling."""
    
    # Core content
    text_content: str
    document_type: str
    
    # Enhanced metadata from Docling
    total_pages: int
    processing_method: str  # "ocr", "text_extraction", "layout_analysis"
    
    # Page-level information
    pages: List[Dict[str, Any]]
    
    # Advanced features
    tables: List[Dict[str, Any]] = field(default_factory=list)
    images: List[Dict[str, Any]] = field(default_factory=list)
    layout_elements: List[Dict[str, Any]] = field(default_factory=list)
    
    # Processing stats
    processing_time_seconds: float = 0.0
    ocr_confidence: float = 0.0
    
    # Original metadata for compatibility
    metadata: Dict[str, Any] = field(default_factory=dict)


class DocumentParser:
    """Standard document parser using Docling with OCR and multi-format support."""
    
    _initialization_logged = False
    
    def __init__(self, config: DoclingConfig = None):
        """Initialize parser with Docling configuration."""
        self.config = config or DoclingConfig()
        self.converter = self._create_converter()
        
        # Only log initialization once per process to reduce spam
        if not DocumentParser._initialization_logged:
            logger.info(f"🚀 DocumentParser initialized with Docling (OCR: {self.config.enable_ocr})")
            DocumentParser._initialization_logged = True
        else:
            logger.debug(f"DocumentParser instance created (OCR: {self.config.enable_ocr})")
        
    def _create_converter(self) -> DocumentConverter:
        """Create Docling DocumentConverter with default settings."""
        
        # Define allowed formats
        allowed_formats = []
        format_map = {
            'pdf': InputFormat.PDF,
            'docx': InputFormat.DOCX, 
            'pptx': InputFormat.PPTX,
            'xlsx': InputFormat.XLSX,
            'html': InputFormat.HTML,
            'png': InputFormat.IMAGE,
            'jpg': InputFormat.IMAGE,
            'jpeg': InputFormat.IMAGE,
            'tiff': InputFormat.IMAGE
        }
        
        for fmt in self.config.supported_formats:
            if fmt.lower() in format_map:
                allowed_formats.append(format_map[fmt.lower()])
        
        # Create converter with default configuration
        converter = DocumentConverter(
            allowed_formats=allowed_formats
        )
        
        return converter
        
    def download_document(self, url: str) -> bytes:
        """
        Download document from URL with enhanced error handling.
        
        Args:
            url: Document URL
            
        Returns:
            Document content as bytes
            
        Raises:
            DocumentParsingError: If download fails
        """
        try:
            logger.info(f"📥 Downloading document: {url}")
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            
            response = requests.get(
                url, 
                headers=headers, 
                timeout=self.config.timeout_seconds,
                stream=True
            )
            response.raise_for_status()
            
            # Check file size
            content = response.content
            size_mb = len(content) / (1024 * 1024)
            
            if size_mb > self.config.max_file_size_mb:
                raise DocumentParsingError(
                    f"Document too large: {size_mb:.1f}MB (max: {self.config.max_file_size_mb}MB)"
                )
            
            logger.info(f"✅ Downloaded {len(content)} bytes ({size_mb:.1f}MB)")
            return content
            
        except requests.exceptions.RequestException as e:
            raise DocumentParsingError(f"Download failed: {str(e)}")
        except Exception as e:
            raise DocumentParsingError(f"Unexpected download error: {str(e)}")
    
    def get_file_type(self, url: str) -> str:
        """Enhanced file type detection."""
        parsed_url = urlparse(url)
        path = parsed_url.path.lower()
        
        # Check extensions
        extensions = {
            '.pdf': 'pdf',
            '.doc': 'docx', '.docx': 'docx',
            '.ppt': 'pptx', '.pptx': 'pptx', 
            '.xls': 'xlsx', '.xlsx': 'xlsx',
            '.html': 'html', '.htm': 'html',
            '.png': 'png', '.jpg': 'jpeg', '.jpeg': 'jpeg', '.tiff': 'tiff',
            '.txt': 'txt'
        }
        
        for ext, file_type in extensions.items():
            if path.endswith(ext):
                return file_type
                
        return 'unknown'
    

    def parse_document(self, source: Union[str, bytes]) -> ParsedDocument:
        """
        Parse document using Docling with advanced features.
        
        Args:
            source: Document URL or bytes content
            
        Returns:
            ParsedDocument with enhanced metadata and content
            
        Raises:
            DocumentParsingError: If parsing fails
        """
        start_time = datetime.now()
        
        try:
            # Handle different input types
            if isinstance(source, str):
                # URL input - validate and determine type
                parsed_url = urlparse(source)
                if not parsed_url.scheme or not parsed_url.netloc:
                    raise DocumentParsingError(f"Invalid URL: {source}")
                    
                document_type = self.get_file_type(source)
                logger.info(f"🔍 Processing URL document type: {document_type}")
                
                # Use Docling's direct URL processing
                conversion_source = source
                
            elif isinstance(source, bytes):
                # Bytes input - save to temp and process
                document_type = 'pdf'  # Assume PDF for bytes
                conversion_source = io.BytesIO(source)
                logger.info(f"🔍 Processing bytes document (assumed PDF)")
                
            else:
                raise DocumentParsingError(f"Unsupported source type: {type(source)}")
            
            # Convert document using Docling
            logger.info(f"🚀 Starting Docling conversion...")
            
            result = self.converter.convert(conversion_source)
            document = result.document
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            # Extract text content
            if self.config.export_format == "markdown":
                text_content = document.export_to_markdown()
                logger.info("📝 Exported to Markdown format")
            else:
                text_content = document.export_to_text()
                logger.info("📝 Exported to plain text format")
            
            # Extract advanced metadata
            pages_info = []
            tables_info = []
            images_info = []
            layout_elements = []
            
            # Process document structure
            if hasattr(document, 'pages'):
                for i, page in enumerate(document.pages):
                    page_info = {
                        'page_number': i + 1,
                        'char_count': len(getattr(page, 'text', '')),
                    }
                    
                    # Add layout information if available
                    if hasattr(page, 'elements'):
                        page_info['elements_count'] = len(page.elements)
                        
                        # Extract tables and images from this page
                        for element in page.elements:
                            element_type = getattr(element, 'label', 'unknown')
                            
                            if 'table' in element_type.lower():
                                tables_info.append({
                                    'page': i + 1,
                                    'type': element_type,
                                    'bbox': getattr(element, 'bbox', None)
                                })
                            elif 'image' in element_type.lower() or 'figure' in element_type.lower():
                                images_info.append({
                                    'page': i + 1, 
                                    'type': element_type,
                                    'bbox': getattr(element, 'bbox', None)
                                })
                            
                            layout_elements.append({
                                'page': i + 1,
                                'type': element_type,
                                'bbox': getattr(element, 'bbox', None)
                            })
                    
                    pages_info.append(page_info)
            
            # Determine processing method
            processing_method = "layout_analysis"
            if self.config.enable_ocr and any('image' in elem.get('type', '').lower() for elem in layout_elements):
                processing_method = "ocr"
            elif text_content and len(text_content.strip()) > 100:
                processing_method = "text_extraction"
            
            # Create enhanced parsed document
            parsed_doc = ParsedDocument(
                text_content=text_content,
                document_type=document_type,
                total_pages=len(pages_info) if pages_info else 1,
                processing_method=processing_method,
                pages=pages_info,
                tables=tables_info,
                images=images_info,
                layout_elements=layout_elements,
                processing_time_seconds=processing_time,
                ocr_confidence=0.95 if processing_method == "ocr" else 1.0,  # Estimated
                metadata={
                    'docling_version': 'latest',
                    'export_format': self.config.export_format,
                    'ocr_enabled': self.config.enable_ocr,
                    'table_structure_enabled': False,
                    'processing_timestamp': datetime.now().isoformat(),
                    'total_characters': len(text_content),
                    'total_tables': len(tables_info),
                    'total_images': len(images_info),
                    'total_layout_elements': len(layout_elements)
                }
            )
            
            logger.info(
                f"✅ Docling parsing completed in {processing_time:.2f}s:\n"
                f"   📄 Pages: {parsed_doc.total_pages}\n" 
                f"   📝 Characters: {len(text_content)}\n"
                f"   📊 Tables: {len(tables_info)}\n"
                f"   🖼️  Images: {len(images_info)}\n"
                f"   🔍 Method: {processing_method}"
            )
            
            return parsed_doc
            
        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"❌ Docling parsing failed after {processing_time:.2f}s: {str(e)}")
            raise DocumentParsingError(f"Docling parsing failed: {str(e)}")