# ------------------------------------------------------------------
# Clean PDF Parser using Unstructured
# ------------------------------------------------------------------
from __future__ import annotations
import logging
import os
import tempfile
import hashlib
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

import requests
from unstructured.partition.pdf import partition_pdf
from unstructured.staging.base import elements_to_json

logging.basicConfig(level=logging.INFO, format="%(levelname)s | %(message)s")
logger = logging.getLogger("clean_pdf_parser")

@dataclass
class DoclingConfig:
    max_file_size_mb: int = 50
    timeout_seconds: int = 60
    extract_tables: bool = True
    include_metadata: bool = True
    # Unstructured specific settings - Production optimized for speed and quality
    strategy: str = "auto"  # "fast" (10s), "auto" (20s), "hi_res" (40s), "ocr_only" (60s)
    extract_images: bool = False
    infer_table_structure: bool = True
    # Performance optimization settings - Enhanced for better content organization
    chunking_strategy: str = "by_title"  # "basic", "by_title", None
    max_characters: int = 2000  # Increased for better context preservation
    new_after_n_chars: int = 1600  # Start new chunk after n chars
    combine_text_under_n_chars: int = 200  # Combine small text blocks
    # Enhanced chunking parameters
    respect_table_boundaries: bool = True  # Keep tables intact
    preserve_section_integrity: bool = True  # Keep sections together
    # Model loading optimization
    skip_infer_table_types: list = None  # Skip certain table inference types
    languages: list = None  # Limit to specific languages for OCR
    # Memory optimization
    extract_image_block_types: list = None  # Limit image extraction types
    # Comprehensive extraction settings
    extract_all_content: bool = True  # Extract ALL text content
    preserve_formatting: bool = True  # Preserve original formatting

@dataclass
class ParsedDocument:
    text_content: str
    document_type: str = "pdf"
    document_id: str = ""
    processing_method: str = "unstructured"
    total_pages: int = 1
    pages: List[Dict[str, Any]] = field(default_factory=list)
    tables: List[Dict[str, Any]] = field(default_factory=list)
    images: List[Dict[str, Any]] = field(default_factory=list)
    layout_elements: List[Dict[str, Any]] = field(default_factory=list)
    elements: List[Dict[str, Any]] = field(default_factory=list)
    processing_time_seconds: float = 0.0
    ocr_confidence: float = 1.0
    metadata: Dict[str, Any] = field(default_factory=dict)

class DocumentParsingError(Exception):
    """Raised when parsing fails."""

class DocumentParser:
    """Clean document parser using Unstructured."""
    
    def __init__(self, config: Optional[DoclingConfig] = None):
        self.config = config or DoclingConfig()
        logger.info("🚀 Clean DocumentParser initialized with Unstructured")
    
    def parse_document(self, source: Union[str, bytes], document_id: str = None) -> ParsedDocument:
        """Parse document using Unstructured."""
        start = datetime.now()
        
        try:
            # Download if URL
            if isinstance(source, str) and source.startswith("http"):
                source = self.download_document(source)
            
            # Generate document ID
            if document_id is None:
                document_id = self._generate_doc_id(source)
            
            # Parse with Unstructured
            elements = self._parse_with_unstructured(source)
            
            # Extract content
            text_content = self._extract_text(elements)
            tables = self._extract_tables(elements)
            
            elapsed = (datetime.now() - start).total_seconds()
            
            # Extract page information from elements
            pages_info = self._extract_pages_info(elements)
            total_pages = len(pages_info) if pages_info else 1

            # Extract images and layout elements (basic implementation)
            images = self._extract_images(elements)
            layout_elements = self._extract_layout_elements(elements)

            # Determine OCR confidence based on strategy
            ocr_confidence = 0.95 if self.config.strategy in ["ocr_only", "hi_res"] else 1.0

            return ParsedDocument(
                document_id=document_id,
                text_content=text_content,
                total_pages=total_pages,
                pages=pages_info,
                tables=tables,
                images=images,
                layout_elements=layout_elements,
                elements=[elem.to_dict() for elem in elements],
                processing_time_seconds=elapsed,
                ocr_confidence=ocr_confidence,
                metadata={
                    "parser_version": "unstructured-1.0.0",
                    "strategy": self.config.strategy,
                    "tables_found": len(tables),
                    "elements_found": len(elements),
                    "processing_timestamp": datetime.now().isoformat(),
                    "content_format": "markdown",
                    "enhanced_table_extraction": True,
                }
            )
            
        except Exception as e:
            elapsed = (datetime.now() - start).total_seconds()
            logger.error("❌ Parsing failed after %.2f s: %s", elapsed, e)
            raise DocumentParsingError(f"Parsing failed: {e}")
    
    def _parse_with_unstructured(self, source: Union[str, bytes]) -> List:
        """Parse document with Unstructured."""
        
        # Handle bytes input
        if isinstance(source, bytes):
            tmp = tempfile.NamedTemporaryFile(suffix=".pdf", delete=False)
            tmp.write(source)
            tmp.close()
            pdf_path = tmp.name
            try:
                return self._parse_pdf_file(pdf_path)
            finally:
                os.remove(pdf_path)
        else:
            return self._parse_pdf_file(source)
    
    def _parse_pdf_file(self, pdf_path: str) -> List:
        """Parse PDF file with Unstructured."""
        logger.info(f"Parsing PDF with strategy: {self.config.strategy}")

        # Build partition_pdf parameters for comprehensive text extraction
        partition_params = {
            "filename": pdf_path,
            "strategy": self.config.strategy,
            "infer_table_structure": self.config.infer_table_structure,
            "extract_images_in_pdf": self.config.extract_images,
            # Enhanced parameters for complete text extraction
            "include_page_breaks": True,
            "include_metadata": True,
            "extract_image_block_types": ["Image", "Table", "FigureCaption"],
            "hi_res_model_name": "yolox",  # Better table detection
            "pdf_infer_table_structure": True,
            "strategy_kwargs": {
                "hi_res_model_name": "yolox",
                "extract_forms": True,
            }
        }

        # Set OCR agent explicitly to avoid OCR errors
        import os
        if not os.getenv("OCR_AGENT"):
            os.environ["OCR_AGENT"] = "unstructured.partition.utils.ocr_models.tesseract_ocr.OCRAgentTesseract"

        # Add chunking parameters for better performance
        if self.config.chunking_strategy:
            partition_params.update({
                "chunking_strategy": self.config.chunking_strategy,
                "max_characters": self.config.max_characters,
                "new_after_n_chars": self.config.new_after_n_chars,
                "combine_text_under_n_chars": self.config.combine_text_under_n_chars,
            })

        # Add image extraction optimization
        if self.config.extract_images and self.config.extract_image_block_types:
            partition_params["extract_image_block_types"] = self.config.extract_image_block_types
        elif self.config.extract_images:
            partition_params["extract_image_block_types"] = ["Image", "Table"]

        # Add language optimization for OCR
        if self.config.languages:
            partition_params["languages"] = self.config.languages

        # Skip certain table inference types for performance
        if self.config.skip_infer_table_types:
            partition_params["skip_infer_table_types"] = self.config.skip_infer_table_types

        try:
            elements = partition_pdf(**partition_params)
        except Exception as e:
            if "OCRAgent" in str(e) or "unstructured_pytesseract" in str(e):
                logger.warning(f"OCR failed, falling back to fast strategy: {e}")
                # Fallback to fast strategy without OCR
                fallback_params = {
                    "filename": pdf_path,
                    "strategy": "fast",
                    "infer_table_structure": False,
                    "extract_images_in_pdf": False,
                }
                elements = partition_pdf(**fallback_params)
            else:
                raise e

        logger.info(f"Found {len(elements)} elements")
        return elements
    
    def _extract_text(self, elements: List) -> str:
        """Extract ALL text from elements and format as well-organized Markdown sections."""
        # Group elements by page and section for better organization
        pages_content = {}

        for element in elements:
            # Get page number
            page_num = None
            if hasattr(element, 'metadata') and hasattr(element.metadata, 'page_number'):
                page_num = element.metadata.page_number

            if page_num not in pages_content:
                pages_content[page_num] = {'sections': []}

            # Extract ALL text content regardless of element type
            element_text = ""
            element_type = getattr(element, 'category', 'Unknown')

            if hasattr(element, 'text') and element.text:
                element_text = element.text.strip()

            # Check for table HTML content
            table_html = None
            if (hasattr(element, 'metadata') and
                hasattr(element.metadata, 'text_as_html') and
                element.metadata.text_as_html):
                table_html = element.metadata.text_as_html

            # Process element based on type and content
            if table_html and '<table>' in table_html:
                # Process as complete table section
                table_markdown = self._extract_organized_table(element_text, table_html)
                pages_content[page_num]['sections'].append({
                    'type': 'table_section',
                    'content': table_markdown,
                    'priority': 'high'  # Tables are high priority for chunking
                })
            elif element_type in ['Title', 'Header'] and element_text:
                pages_content[page_num]['sections'].append({
                    'type': 'header',
                    'content': element_text,
                    'priority': 'medium'
                })
            elif element_text:
                # Capture ALL other text content
                pages_content[page_num]['sections'].append({
                    'type': 'text',
                    'content': element_text,
                    'priority': 'low'
                })

        # Build well-organized Markdown output
        return self._build_organized_markdown(pages_content)

    def _build_organized_markdown(self, pages_content: dict) -> str:
        """Build well-organized Markdown with proper section boundaries."""
        markdown_sections = []

        for page_num in sorted([p for p in pages_content.keys() if p is not None]):
            page_sections = []
            page_sections.append(f"# Page {page_num}")

            # Group related content together
            current_section = []
            current_section_type = None

            for section in pages_content[page_num]['sections']:
                section_type = section['type']
                content = section['content']

                if section_type == 'table_section':
                    # Tables get their own complete section
                    if current_section:
                        page_sections.append('\n'.join(current_section))
                        current_section = []
                    page_sections.append(content)
                    current_section_type = None

                elif section_type == 'header':
                    # Headers start new sections
                    if current_section:
                        page_sections.append('\n'.join(current_section))
                        current_section = []

                    # Determine header level
                    if any(keyword in content.upper() for keyword in ['JABATAN', 'REKOD HARIAN']):
                        current_section.append(f"\n## {content}")
                    elif any(keyword in content.upper() for keyword in ['BILANGAN PEKERJA', 'BAHAN-BAHAN', 'LOJI']):
                        current_section.append(f"\n### {content}")
                    else:
                        current_section.append(f"\n#### {content}")
                    current_section_type = 'header'

                else:
                    # Regular text content
                    current_section.append(content)
                    current_section_type = 'text'

            # Add any remaining content
            if current_section:
                page_sections.append('\n'.join(current_section))

            # Join page sections with proper spacing
            markdown_sections.append('\n\n'.join(page_sections))

        return '\n\n---\n\n'.join(markdown_sections)

    def _extract_organized_table(self, element_text: str, table_html: str) -> str:
        """Extract table content in a well-organized format for better chunking."""
        try:
            import re
            from html import unescape

            # Start with a clear table section
            table_title = self._extract_table_title(element_text)
            result = [f"### {table_title}"]

            # Add clean raw content (condensed)
            if element_text:
                clean_text = re.sub(r'\s+', ' ', element_text).strip()
                # Limit raw content to avoid repetition
                if len(clean_text) > 500:
                    clean_text = clean_text[:500] + "..."
                result.append(f"\n**Content:** {clean_text}")

            # Create clean Markdown table
            table_markdown = self._html_table_to_clean_markdown(table_html)
            if table_markdown:
                result.append(f"\n{table_markdown}")

            # Add key extracted data (condensed)
            key_numbers = re.findall(r'\b\d+\b', element_text)
            if key_numbers:
                # Limit to most relevant numbers (avoid repetition)
                unique_numbers = list(dict.fromkeys(key_numbers))[:15]  # First 15 unique numbers
                result.append(f"\n**Key Numbers:** {', '.join(unique_numbers)}")

            # Extract specific categories
            patterns = {
                'Worker Types': r'(CBR In-Situ Work|Subgrade Level Preparation|Site Safety|Install cones)',
                'Materials': r'(Coarse Sand|Concrete G25JKR|Load)',
                'Equipment': r'(Excavator|Dumptruck|Compactor|Backhoe|Road roller|Grader)',
            }

            for pattern_name, pattern in patterns.items():
                matches = re.findall(pattern, element_text, re.IGNORECASE)
                if matches:
                    unique_matches = list(dict.fromkeys(matches))
                    result.append(f"\n**{pattern_name}:** {', '.join(unique_matches)}")

            return '\n'.join(result)

        except Exception as e:
            logger.debug(f"Error in organized table extraction: {e}")
            return f"### Table\n\n{element_text}"

    def _html_table_to_clean_markdown(self, html_content: str) -> str:
        """Convert HTML table to clean, well-formatted Markdown."""
        try:
            import re
            from html import unescape

            # Extract table rows
            row_pattern = r'<tr[^>]*>(.*?)</tr>'
            rows = re.findall(row_pattern, html_content, re.DOTALL)

            if not rows:
                return None

            markdown_rows = []
            header_added = False

            for row_html in rows:
                # Extract cells
                cell_pattern = r'<(?:th|td)[^>]*>(.*?)</(?:th|td)>'
                cells = re.findall(cell_pattern, row_html, re.DOTALL)

                if not cells:
                    continue

                # Clean and format cells
                cleaned_cells = []
                for cell in cells:
                    clean_cell = re.sub(r'<[^>]+>', '', cell)
                    clean_cell = unescape(clean_cell).strip()
                    # Replace empty cells with "-" for better readability
                    if not clean_cell:
                        clean_cell = "-"
                    # Limit cell length to avoid overly wide tables
                    if len(clean_cell) > 50:
                        clean_cell = clean_cell[:47] + "..."
                    cleaned_cells.append(clean_cell)

                if cleaned_cells and any(cell != "-" for cell in cleaned_cells):
                    # Create table row
                    markdown_row = "| " + " | ".join(cleaned_cells) + " |"
                    markdown_rows.append(markdown_row)

                    # Add header separator after first meaningful row
                    if not header_added:
                        separator = "| " + " | ".join(["---"] * len(cleaned_cells)) + " |"
                        markdown_rows.append(separator)
                        header_added = True

            return "\n".join(markdown_rows) if markdown_rows else None

        except Exception as e:
            logger.debug(f"Could not convert HTML table to clean Markdown: {e}")
            return None

    def _extract_comprehensive_table(self, element_text: str, table_html: str) -> str:
        """Extract comprehensive table content including ALL text and values."""
        try:
            import re
            from html import unescape

            # Start with table title
            table_title = self._extract_table_title(element_text)
            result = [f"### {table_title}"]

            # Extract ALL text content from the element first
            if element_text:
                # Clean and format the raw text content
                clean_text = re.sub(r'\s+', ' ', element_text).strip()
                result.append(f"\n**Raw Content:** {clean_text}")

            # Try to parse HTML table structure
            table_markdown = self._html_table_to_markdown(table_html, element_text)
            if table_markdown and "| --- |" in table_markdown:
                # Extract just the table part without the title
                table_lines = table_markdown.split('\n')
                table_only = []
                in_table = False
                for line in table_lines:
                    if line.startswith('|') or line.startswith('| ---'):
                        in_table = True
                        table_only.append(line)
                    elif in_table and not line.strip():
                        break

                if table_only:
                    result.append("\n**Structured Table:**")
                    result.extend(table_only)

            # Extract ALL numerical values
            all_numbers = re.findall(r'\b\d+\b', element_text + ' ' + table_html)
            if all_numbers:
                result.append(f"\n**All Numbers Found:** {', '.join(all_numbers)}")

            # Extract specific patterns for Malaysian construction docs
            patterns = {
                'Worker Types': r'(CBR In-Situ Work|Subgrade Level Preparation|Site Safety|Install cones)',
                'Materials': r'(Coarse Sand|Concrete G25JKR|Load)',
                'Equipment': r'(Excavator|Dumptruck|Compactor|Backhoe|Road roller|Grader)',
                'Quantities': r'(\d+\s*Load|\d+m3|\d+\s*hours?)',
            }

            for pattern_name, pattern in patterns.items():
                matches = re.findall(pattern, element_text, re.IGNORECASE)
                if matches:
                    result.append(f"\n**{pattern_name}:** {', '.join(set(matches))}")

            return '\n'.join(result)

        except Exception as e:
            logger.debug(f"Error in comprehensive table extraction: {e}")
            return f"### Table\n\n{element_text}"

    def _process_table_to_markdown(self, element) -> str:
        """Convert table element to structured Markdown format."""
        table_text = element.text if hasattr(element, 'text') else ''

        # Try to extract HTML table structure and convert to Markdown
        if hasattr(element, 'metadata') and hasattr(element.metadata, 'text_as_html'):
            html_content = element.metadata.text_as_html
            markdown_table = self._html_table_to_markdown(html_content, table_text)
            if markdown_table:
                return markdown_table

        # Fallback: process as enhanced text with extracted values
        enhanced_text = self._process_table_element(element)
        return f"### Table\n\n{enhanced_text}"

    def _html_table_to_markdown(self, html_content: str, fallback_text: str) -> str:
        """Convert HTML table to Markdown table format."""
        try:
            import re
            from html import unescape

            # Extract table rows
            row_pattern = r'<tr[^>]*>(.*?)</tr>'
            rows = re.findall(row_pattern, html_content, re.DOTALL)

            if not rows:
                return None

            markdown_rows = []
            header_processed = False

            for row_html in rows:
                # Extract cells (both th and td)
                cell_pattern = r'<(?:th|td)[^>]*>(.*?)</(?:th|td)>'
                cells = re.findall(cell_pattern, row_html, re.DOTALL)

                if not cells:
                    continue

                # Clean cell content
                cleaned_cells = []
                for cell in cells:
                    # Remove HTML tags and clean whitespace
                    clean_cell = re.sub(r'<[^>]+>', '', cell)
                    clean_cell = unescape(clean_cell).strip()
                    # Replace empty cells with spaces for better formatting
                    if not clean_cell:
                        clean_cell = " "
                    cleaned_cells.append(clean_cell)

                if cleaned_cells:
                    # Create Markdown table row
                    markdown_row = "| " + " | ".join(cleaned_cells) + " |"
                    markdown_rows.append(markdown_row)

                    # Add header separator after first row
                    if not header_processed:
                        separator = "| " + " | ".join(["---"] * len(cleaned_cells)) + " |"
                        markdown_rows.append(separator)
                        header_processed = True

            if markdown_rows:
                # Determine table title from content
                table_title = self._extract_table_title(fallback_text)
                result = f"### {table_title}\n\n" + "\n".join(markdown_rows)

                # Add extracted numerical values as metadata
                numbers = re.findall(r'\b\d+\b', fallback_text)
                if numbers:
                    result += f"\n\n**Numerical Values:** {', '.join(numbers)}"

                return result

        except Exception as e:
            logger.debug(f"Could not convert HTML table to Markdown: {e}")

        return None

    def _extract_table_title(self, text: str) -> str:
        """Extract a meaningful title from table text."""
        # Look for common Malaysian construction terms
        if 'BILANGAN PEKERJA' in text.upper():
            return "Bilangan Pekerja di Tapak Bina (Worker Count)"
        elif 'BAHAN-BAHAN' in text.upper():
            return "Bahan-Bahan Binaan (Construction Materials)"
        elif 'LOJI' in text.upper() or 'ALAT' in text.upper():
            return "Loji, Alat dan Kelengkapan (Equipment and Machinery)"
        elif 'KERJA YANG TERGENDALA' in text.upper():
            return "Kerja Yang Tergendala (Delayed Work)"
        else:
            return "Table"

    def _process_table_element(self, element) -> str:
        """Process table element to extract better structured data including numerical values."""
        table_text = element.text if hasattr(element, 'text') else ''

        # Try to extract HTML table structure if available
        if hasattr(element, 'metadata') and hasattr(element.metadata, 'text_as_html'):
            html_content = element.metadata.text_as_html
            # Parse HTML table to extract cell values more accurately
            try:
                import re
                # Extract table cells from HTML
                cell_pattern = r'<td[^>]*>(.*?)</td>'
                cells = re.findall(cell_pattern, html_content, re.DOTALL)

                if cells:
                    # Clean up cell content and format as structured text
                    cleaned_cells = []
                    for cell in cells:
                        # Remove HTML tags and clean whitespace
                        clean_cell = re.sub(r'<[^>]+>', '', cell).strip()
                        if clean_cell:
                            cleaned_cells.append(clean_cell)

                    if cleaned_cells:
                        # Add extracted cell values to the table text
                        table_text += f"\n\nExtracted Values: {' | '.join(cleaned_cells)}"

            except Exception as e:
                logger.debug(f"Could not parse HTML table: {e}")

        # Also try to extract any numerical patterns from the original text
        if table_text:
            import re
            # Look for numerical values that might be worker counts
            numbers = re.findall(r'\b\d+\b', table_text)
            if numbers:
                table_text += f"\n\nNumerical Values Found: {', '.join(numbers)}"

        return table_text
    
    def _extract_tables(self, elements: List) -> List[Dict[str, Any]]:
        """Extract tables from elements with enhanced data extraction."""
        tables = []

        for i, element in enumerate(elements):
            if hasattr(element, 'category') and element.category == 'Table':

                table_data = {
                    'table_index': i,
                    'text': element.text if hasattr(element, 'text') else '',
                    'metadata': element.metadata.to_dict() if hasattr(element, 'metadata') else {},
                }

                # Try to extract structured table data if available
                if hasattr(element, 'metadata') and hasattr(element.metadata, 'text_as_html'):
                    table_data['html'] = element.metadata.text_as_html

                    # Extract cell values from HTML
                    try:
                        import re
                        cell_pattern = r'<td[^>]*>(.*?)</td>'
                        cells = re.findall(cell_pattern, element.metadata.text_as_html, re.DOTALL)
                        cleaned_cells = []
                        for cell in cells:
                            clean_cell = re.sub(r'<[^>]+>', '', cell).strip()
                            if clean_cell:
                                cleaned_cells.append(clean_cell)
                        table_data['extracted_cells'] = cleaned_cells

                        # Extract numerical values specifically
                        numbers = []
                        for cell in cleaned_cells:
                            cell_numbers = re.findall(r'\b\d+\b', cell)
                            numbers.extend(cell_numbers)
                        table_data['numerical_values'] = numbers

                    except Exception as e:
                        logger.debug(f"Could not extract table cells: {e}")

                # Extract page number if available
                if hasattr(element, 'metadata') and hasattr(element.metadata, 'page_number'):
                    table_data['page'] = element.metadata.page_number

                tables.append(table_data)
                logger.info(f"Found table {i+1} on page {table_data.get('page', 'unknown')} with {len(table_data.get('numerical_values', []))} numerical values")

        return tables

    def _extract_pages_info(self, elements: List) -> List[Dict[str, Any]]:
        """Extract page information from elements."""
        pages_info = {}

        for element in elements:
            if hasattr(element, 'metadata') and hasattr(element.metadata, 'page_number'):
                page_num = element.metadata.page_number
                if page_num not in pages_info:
                    pages_info[page_num] = {
                        'page_number': page_num,
                        'char_count': 0,
                        'elements_count': 0
                    }

                # Count characters and elements per page
                if hasattr(element, 'text') and element.text:
                    pages_info[page_num]['char_count'] += len(element.text)
                pages_info[page_num]['elements_count'] += 1

        # Convert to sorted list, filtering out None page numbers
        valid_page_nums = [page_num for page_num in pages_info.keys() if page_num is not None]
        return [pages_info[page_num] for page_num in sorted(valid_page_nums)]

    def _extract_images(self, elements: List) -> List[Dict[str, Any]]:
        """Extract image information from elements."""
        images = []

        for i, element in enumerate(elements):
            if hasattr(element, 'category') and 'Image' in str(element.category):
                image_data = {
                    'image_id': f"img_{i+1}",
                    'type': str(element.category),
                    'page': getattr(element.metadata, 'page_number', 1) if hasattr(element, 'metadata') else 1
                }
                images.append(image_data)

        return images

    def _extract_layout_elements(self, elements: List) -> List[Dict[str, Any]]:
        """Extract layout element information."""
        layout_elements = []

        for i, element in enumerate(elements):
            if hasattr(element, 'category'):
                layout_data = {
                    'element_id': f"layout_{i+1}",
                    'type': str(element.category),
                    'page': getattr(element.metadata, 'page_number', 1) if hasattr(element, 'metadata') else 1,
                    'char_count': len(element.text) if hasattr(element, 'text') and element.text else 0
                }
                layout_elements.append(layout_data)

        return layout_elements

    def _generate_doc_id(self, source: Union[str, bytes]) -> str:
        """Generate a unique document ID."""
        if isinstance(source, bytes):
            content_hash = hashlib.md5(source).hexdigest()
        else:
            content_hash = hashlib.md5(str(source).encode()).hexdigest()
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"doc_{timestamp}_{content_hash[:8]}"
    
    def download_document(self, url: str) -> bytes:
        """Download document from URL."""
        try:
            logger.info("📥 Downloading %s", url)
            headers = {"User-Agent": "Mozilla/5.0"}
            response = requests.get(
                url, headers=headers, timeout=self.config.timeout_seconds, stream=True
            )
            response.raise_for_status()
            content = response.content
            size_mb = len(content) / (1024 * 1024)
            
            if size_mb > self.config.max_file_size_mb:
                raise DocumentParsingError(
                    f"Document too large: {size_mb:.1f} MB (max {self.config.max_file_size_mb} MB)"
                )
            
            logger.info("✅ Downloaded %s bytes (%.1f MB)", len(content), size_mb)
            return content
        except requests.RequestException as e:
            raise DocumentParsingError(f"Download failed: {e}")

# ------------------------------------------------------------------
# Example Usage
# ------------------------------------------------------------------
if __name__ == "__main__":
    import sys
    
    if len(sys.argv) != 2:
        print("Usage: python clean_pdf_parser.py <path_to_pdf>")
        sys.exit(1)
    
    pdf_path = sys.argv[1]
    
    if not Path(pdf_path).exists():
        print(f"Error: File {pdf_path} not found")
        sys.exit(1)
    
    # Configure parser
    config = DoclingConfig(
        strategy="hi_res",  # Use high-resolution strategy
        infer_table_structure=True,
        extract_tables=True
    )
    
    # Parse document
    parser = DocumentParser(config)
    result = parser.parse_document(pdf_path)
    
    # Print results
    print("=" * 80)
    print("UNSTRUCTURED PARSING RESULTS")
    print("=" * 80)
    print(f"Document ID: {result.document_id}")
    print(f"Processing Time: {result.processing_time_seconds:.2f} seconds")
    print(f"Elements Found: {len(result.elements)}")
    print(f"Tables Found: {len(result.tables)}")
    
    # Show tables
    if result.tables:
        print("\n" + "=" * 80)
        print("EXTRACTED TABLES")
        print("=" * 80)
        
        for i, table in enumerate(result.tables):
            print(f"\n--- Table {i+1} (Page {table.get('page', 'unknown')}) ---")
            print(table['text'])
            print("-" * 60)
    
    # Show sample text
    print("\n" + "=" * 80)
    print("SAMPLE EXTRACTED TEXT")
    print("=" * 80)
    print(result.text_content[:1000])
    if len(result.text_content) > 1000:
        print("...")
    
    print(f"\nTotal text length: {len(result.text_content)} characters")