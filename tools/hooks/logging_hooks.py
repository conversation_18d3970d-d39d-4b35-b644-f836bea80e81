"""
A<PERSON> hooks for logging and monitoring tool execution.
"""
import time
import json
from typing import Any, Dict, Callable
from datetime import datetime
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def tool_execution_logger(function_name: str, function_call: Callable, arguments: Dict[str, Any]) -> Any:
    """
    Log tool execution with timing and arguments.
    
    Args:
        function_name: Name of the function being called
        function_call: The actual function to execute
        arguments: Arguments passed to the function
    
    Returns:
        Result of the function call
    """
    start_time = time.time()
    timestamp = datetime.now().isoformat()
    
    # Log the start
    logger.info(f"🔧 TOOL START | {function_name} | {timestamp}")
    logger.info(f"📋 ARGS | {json.dumps(arguments, default=str, indent=2)}")
    print(f"[LOGGING HOOK DEBUG] Tool {function_name} starting with args: {arguments}")
    
    try:
        # Execute the function
        result = await function_call(**arguments)
        
        # Calculate execution time
        duration = time.time() - start_time
        
        # Log success
        logger.info(f"✅ TOOL SUCCESS | {function_name} | Duration: {duration:.2f}s")
        
        # Log result summary (truncate if too long)
        result_str = str(result)
        if len(result_str) > 200:
            result_summary = result_str[:200] + "..."
        else:
            result_summary = result_str
        logger.info(f"📤 RESULT | {result_summary}")
        
        return result
        
    except Exception as e:
        # Calculate execution time even on error
        duration = time.time() - start_time
        
        # Log error
        logger.error(f"❌ TOOL ERROR | {function_name} | Duration: {duration:.2f}s")
        logger.error(f"🚨 ERROR | {str(e)}")
        
        # Re-raise the exception
        raise


async def vector_search_monitor(function_name: str, function_call: Callable, arguments: Dict[str, Any]) -> Any:
    """
    Special monitoring hook for vector search operations.
    
    Tracks search queries, results count, and performance metrics.
    """
    if function_name not in ["search_documents_vector", "search_documents_vector_optimized"]:
        # Not a vector search, just execute normally
        return await function_call(**arguments)
    
    start_time = time.time()
    
    # Extract search parameters
    query_text = arguments.get('query_text', 'N/A')
    project_id = arguments.get('project_id', 'N/A')
    document_type = arguments.get('document_type', 'any')
    limit = arguments.get('limit', 100)
    
    logger.info("🔍 VECTOR SEARCH START")
    logger.info(f"   Query: '{query_text}'")
    logger.info(f"   Project: {project_id}")
    logger.info(f"   Type: {document_type}")
    logger.info(f"   Limit: {limit}")
    
    try:
        # Execute the search
        result = await function_call(**arguments)
        duration = time.time() - start_time
        
        # Extract result metrics
        if isinstance(result, dict):
            total_count = result.get('total_count', 0)
            success = result.get('success', False)
            search_method = result.get('search_method', 'unknown')
        else:
            total_count = 0
            success = False
            search_method = 'unknown'
        
        # Log results
        logger.info("🎯 VECTOR SEARCH COMPLETE")
        logger.info(f"   Found: {total_count} documents")
        logger.info(f"   Success: {success}")
        logger.info(f"   Method: {search_method}")
        logger.info(f"   Duration: {duration:.2f}s")
        
        return result
        
    except Exception as e:
        duration = time.time() - start_time
        logger.error(f"🚨 VECTOR SEARCH ERROR | Duration: {duration:.2f}s")
        logger.error(f"   Error: {str(e)}")
        raise


async def document_processing_monitor(function_name: str, function_call: Callable, arguments: Dict[str, Any]) -> Any:
    """
    Monitor document processing operations like parsing and vectorization.
    """
    if function_name not in ["parse_site_diary_dates", "generate_embedding"]:
        return await function_call(**arguments)
    
    start_time = time.time()
    
    logger.info(f"📄 DOCUMENT PROCESSING | {function_name}")
    
    if function_name == "parse_site_diary_dates":
        query = arguments.get('query', 'N/A')
        logger.info(f"   Parsing query: '{query}'")
    
    try:
        result = await function_call(**arguments)
        duration = time.time() - start_time
        
        if function_name == "parse_site_diary_dates" and isinstance(result, dict):
            has_date = result.get('has_date_range', False)
            query_type = result.get('query_type', 'unknown')
            logger.info(f"   Date found: {has_date}")
            logger.info(f"   Query type: {query_type}")
        
        logger.info(f"✅ PROCESSING COMPLETE | Duration: {duration:.2f}s")
        return result
        
    except Exception as e:
        duration = time.time() - start_time
        logger.error(f"❌ PROCESSING ERROR | {function_name} | Duration: {duration:.2f}s")
        logger.error(f"   Error: {str(e)}")
        raise


async def validation_hook(function_name: str, function_call: Callable, arguments: Dict[str, Any]) -> Any:
    """
    Validate function arguments before execution.
    """
    # Validation for vector search
    if function_name == "search_documents_vector":
        query_text = arguments.get('query_text', '')
        if not query_text or len(query_text.strip()) == 0:
            logger.warning("⚠️  VALIDATION | Empty query text for vector search")
            # You could modify arguments or raise an exception here
            arguments['query_text'] = "document"  # Default query
    
    # Validation for date parsing
    if function_name == "parse_site_diary_dates":
        query = arguments.get('query', '')
        if not query:
            logger.warning("⚠️  VALIDATION | Empty query for date parsing")
    
    # Execute the function
    return function_call(**arguments)


async def performance_tracker(function_name: str, function_call: Callable, arguments: Dict[str, Any]) -> Any:
    """
    Track performance metrics for all tool calls.
    """
    start_time = time.time()
    
    try:
        result = await function_call(**arguments)
        duration = time.time() - start_time
        
        # Log performance metrics
        if duration > 2.0:  # Slow operations
            logger.warning(f"🐌 SLOW TOOL | {function_name} | {duration:.2f}s")
        elif duration > 0.5:  # Medium operations
            logger.info(f"⏱️  MEDIUM TOOL | {function_name} | {duration:.2f}s")
        else:  # Fast operations
            logger.debug(f"⚡ FAST TOOL | {function_name} | {duration:.2f}s")
        
        return result
        
    except Exception as e:
        duration = time.time() - start_time
        logger.error(f"💥 FAILED TOOL | {function_name} | {duration:.2f}s | {str(e)}")
        raise