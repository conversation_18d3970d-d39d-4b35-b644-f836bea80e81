"""
Tool hooks for monitoring and logging tool execution.
Provides pre and post execution hooks for Agno tools.
"""
import json
import time
from datetime import datetime
from logging import getLogger

from agno.tools import FunctionCall

logger = getLogger(__name__)


def tool_pre_hook(fc: FunctionCall) -> None:
    """
    Pre-execution hook that logs tool invocation details.
    
    Args:
        fc: FunctionCall object containing function details
    """
    try:
        # Log tool invocation
        logger.info(f"🔧 PRE-HOOK: Executing tool '{fc.function.name}'")
        
        # Log arguments if available
        if fc.arguments:
            # Try to format arguments nicely
            if isinstance(fc.arguments, dict):
                args_str = json.dumps(fc.arguments, indent=2)
            else:
                args_str = str(fc.arguments)
            logger.info(f"📥 Arguments: {args_str}")
        
        # Store start time in function context for duration calculation
        if hasattr(fc, '__dict__'):
            fc.__dict__['_start_time'] = time.time()
            
    except Exception as e:
        logger.error(f"❌ Error in pre-hook for {fc.function.name}: {e}")


def tool_post_hook(fc: FunctionCall) -> None:
    """
    Post-execution hook that logs tool results and performance.
    
    Args:
        fc: FunctionCall object containing function details and results
    """
    try:
        # Calculate duration if start time was stored
        duration_ms = None
        if hasattr(fc, '__dict__') and '_start_time' in fc.__dict__:
            duration_ms = (time.time() - fc.__dict__['_start_time']) * 1000
            duration_str = f" (Duration: {duration_ms:.2f}ms)"
        else:
            duration_str = ""
        
        # Log completion
        logger.info(f"✅ POST-HOOK: Completed tool '{fc.function.name}'{duration_str}")
        
        # Log result summary
        if fc.result:
            # Handle different result types
            if isinstance(fc.result, dict):
                result_summary = f"Returned dict with {len(fc.result)} keys"
                if 'success' in fc.result:
                    result_summary += f" (success={fc.result['success']})"
                if 'total_count' in fc.result:
                    result_summary += f" (count={fc.result['total_count']})"
            elif isinstance(fc.result, list):
                result_summary = f"Returned list with {len(fc.result)} items"
            elif isinstance(fc.result, str):
                # Truncate long strings
                if len(fc.result) > 200:
                    result_summary = f"Returned string: {fc.result[:200]}..."
                else:
                    result_summary = f"Returned string: {fc.result}"
            else:
                result_summary = f"Returned {type(fc.result).__name__}"
                
            logger.info(f"📤 Result: {result_summary}")
            
    except Exception as e:
        logger.error(f"❌ Error in post-hook for {fc.function.name}: {e}")


def debug_pre_hook(fc: FunctionCall) -> None:
    """
    Detailed pre-execution hook for debugging.
    Logs extensive details about the tool call.
    """
    try:
        logger.debug(f"🔍 DEBUG PRE-HOOK: {fc.function.name}")
        logger.debug(f"  Function object: {fc.function}")
        logger.debug(f"  Arguments type: {type(fc.arguments)}")
        logger.debug(f"  Full arguments: {json.dumps(fc.arguments, default=str, indent=2)}")
        
        # Log context if available
        if hasattr(fc, 'agent') and fc.agent:
            if hasattr(fc.agent, 'context'):
                logger.debug(f"  Agent context: {fc.agent.context}")
                
    except Exception as e:
        logger.error(f"❌ Error in debug pre-hook: {e}")


def debug_post_hook(fc: FunctionCall) -> None:
    """
    Detailed post-execution hook for debugging.
    Logs extensive details about the tool result.
    """
    try:
        logger.debug(f"🔍 DEBUG POST-HOOK: {fc.function.name}")
        logger.debug(f"  Result type: {type(fc.result)}")
        
        # Try to serialize result for logging
        if fc.result:
            try:
                result_json = json.dumps(fc.result, default=str, indent=2)
                # Limit to 1000 chars for debugging
                if len(result_json) > 1000:
                    result_json = result_json[:1000] + "..."
                logger.debug(f"  Full result: {result_json}")
            except:
                logger.debug(f"  Result (non-JSON): {str(fc.result)[:1000]}")
                
    except Exception as e:
        logger.error(f"❌ Error in debug post-hook: {e}")


def performance_pre_hook(fc: FunctionCall) -> None:
    """
    Performance monitoring pre-hook.
    Records detailed timing information.
    """
    if hasattr(fc, '__dict__'):
        fc.__dict__['_perf_start'] = {
            'time': time.time(),
            'timestamp': datetime.now().isoformat(),
            'tool': fc.function.name,
            'args_size': len(str(fc.arguments)) if fc.arguments else 0
        }


def performance_post_hook(fc: FunctionCall) -> None:
    """
    Performance monitoring post-hook.
    Calculates and logs performance metrics.
    """
    try:
        if hasattr(fc, '__dict__') and '_perf_start' in fc.__dict__:
            perf_data = fc.__dict__['_perf_start']
            duration_ms = (time.time() - perf_data['time']) * 1000
            
            # Determine performance rating
            if duration_ms < 100:
                rating = "⚡ FAST"
            elif duration_ms < 500:
                rating = "✅ GOOD"
            elif duration_ms < 2000:
                rating = "⚠️ SLOW"
            else:
                rating = "🐌 VERY SLOW"
            
            # Calculate result size
            result_size = len(str(fc.result)) if fc.result else 0
            
            logger.info(f"📊 PERFORMANCE: {fc.function.name}")
            logger.info(f"  Duration: {duration_ms:.2f}ms {rating}")
            logger.info(f"  Args size: {perf_data['args_size']} bytes")
            logger.info(f"  Result size: {result_size} bytes")
            
            # Log warning for slow tools
            if duration_ms > 2000:
                logger.warning(f"⚠️ Tool '{fc.function.name}' took {duration_ms:.2f}ms - consider optimization")
                
    except Exception as e:
        logger.error(f"❌ Error in performance post-hook: {e}")


def error_handling_pre_hook(fc: FunctionCall) -> None:
    """
    Error handling pre-hook.
    Sets up error tracking context.
    """
    if hasattr(fc, '__dict__'):
        fc.__dict__['_error_context'] = {
            'tool': fc.function.name,
            'started_at': datetime.now().isoformat(),
            'arguments': fc.arguments
        }


def error_handling_post_hook(fc: FunctionCall) -> None:
    """
    Error handling post-hook.
    Checks for errors and logs them appropriately.
    """
    try:
        # Check if result indicates an error
        if fc.result and isinstance(fc.result, dict):
            if fc.result.get('error') or fc.result.get('success') is False:
                error_msg = fc.result.get('error') or fc.result.get('message', 'Unknown error')
                logger.error(f"❌ Tool '{fc.function.name}' returned error: {error_msg}")
                
                # Log context if available
                if hasattr(fc, '__dict__') and '_error_context' in fc.__dict__:
                    context = fc.__dict__['_error_context']
                    logger.error(f"  Error context: {json.dumps(context, default=str, indent=2)}")
                    
    except Exception as e:
        logger.error(f"❌ Error in error handling post-hook: {e}")


# Composite hooks that combine multiple behaviors
def standard_pre_hook(fc: FunctionCall) -> None:
    """Standard pre-hook combining logging and performance tracking."""
    tool_pre_hook(fc)
    performance_pre_hook(fc)
    error_handling_pre_hook(fc)


def standard_post_hook(fc: FunctionCall) -> None:
    """Standard post-hook combining logging, performance, and error handling."""
    tool_post_hook(fc)
    performance_post_hook(fc)
    error_handling_post_hook(fc)


def verbose_pre_hook(fc: FunctionCall) -> None:
    """Verbose pre-hook with debug information."""
    tool_pre_hook(fc)
    debug_pre_hook(fc)
    performance_pre_hook(fc)
    error_handling_pre_hook(fc)


def verbose_post_hook(fc: FunctionCall) -> None:
    """Verbose post-hook with debug information."""
    tool_post_hook(fc)
    debug_post_hook(fc)
    performance_post_hook(fc)
    error_handling_post_hook(fc)