"""
Date requirement validation for document searches.
"""
from typing import Dict, Any
from agno.tools.function import Function
import logging

from ..hooks.tool_hooks import standard_pre_hook, standard_post_hook

logger = logging.getLogger(__name__)

def _validate_date_requirement_impl(
    query: str,
    document_type: str = None
) -> Dict[str, Any]:
    """
    Validate if a date range is required for the given search query.
    
    Args:
        query: User's search query
        document_type: Optional document type filter
    
    Returns:
        Dictionary with validation results and guidance
    """
    query_lower = query.lower().strip()
    
    # Check if this is a site diary request
    is_site_diary_request = any(keyword in query_lower for keyword in [
        'site diary', 'diary', 'diaries', 'site diaries'
    ])
    
    # If document_type is explicitly site_diary
    if document_type and 'diary' in document_type.lower():
        is_site_diary_request = True
    
    # Check if query already contains date information
    has_date_info = any(keyword in query_lower for keyword in [
        'last week', 'this week', 'last month', 'this month',
        'last year', 'this year', 'yesterday', 'today',
        'recent', 'latest', 'past', 'from', 'to', 'until',
        'january', 'february', 'march', 'april', 'may', 'june',
        'july', 'august', 'september', 'october', 'november', 'december',
        '2024', '2025', '2026'  # Add relevant years
    ]) or any(char in query for char in ['-', '/', ':'])  # Date format indicators
    
    if is_site_diary_request and not has_date_info:
        return {
            'requires_date': True,
            'document_type': 'site_diary',
            'error_message': 'To search site diaries effectively, please specify a date range (based on submission/upload date).',
            'user_message': 'Please specify a date range for your site diary search (based on when documents were uploaded):',
            'suggestions': [
                'Site diary from last week (uploaded last week)',
                'Site diary for August 2025 (uploaded in August)',
                'Site diary from 2025-08-20 to 2025-08-25 (uploaded in this range)',
                'Recent site diary entries (recently uploaded)'
            ],
            'should_block_search': True,
            'validation_failed': True
        }
    
    return {
        'requires_date': False,
        'document_type': document_type or 'general',
        'should_block_search': False,
        'validation_passed': True
    }

# Create the Function object
validate_date_requirement = Function(
    name="validate_date_requirement",
    entrypoint=_validate_date_requirement_impl,
    description="""Validate if a date range is required for the given document search query.
    
This tool helps determine when users need to specify dates for certain types of document searches, particularly site diaries.""",
    parameters={
        "type": "object",
        "properties": {
            "query": {
                "type": "string",
                "description": "User's search query to validate"
            },
            "document_type": {
                "type": "string", 
                "description": "Optional document type filter (e.g., 'site_diary')"
            }
        },
        "required": ["query"]
    },
    pre_hook=standard_pre_hook,
    post_hook=standard_post_hook,
    show_result=False
)