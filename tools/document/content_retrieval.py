"""
Document content retrieval tool for analysis.
"""
from typing import Dict, Any, List
from agno.tools.function import Function
import logging

logger = logging.getLogger(__name__)


def _retrieve_document_content_impl(
    document_names: List[str],
    project_id: str,
    max_documents: int = 10
) -> Dict[str, Any]:
    """
    Retrieve full content from multiple documents for analysis.
    
    Args:
        document_names: List of document names to retrieve content for
        project_id: Project ID to filter documents
        max_documents: Maximum number of documents to retrieve (performance limit)
    
    Returns:
        Dict with document contents and metadata
    """
    try:
        from tools.vector_db.milvus_client_optimized import get_optimized_vector_db
        
        # Get vector database client
        vector_db = get_optimized_vector_db()
        
        # Limit documents for performance
        limited_docs = document_names[:max_documents]
        
        logger.info(f"🔍 Retrieving content for {len(limited_docs)} documents")
        
        documents_content = []
        
        for doc_name in limited_docs:
            try:
                # Create a dummy embedding for search
                from tools.vector_db.services.embedding_service_optimized import get_optimized_embedding_service
                embedding_service = get_optimized_embedding_service()
                dummy_embedding = embedding_service.generate_embedding("document")
                
                # Search for the specific document using search_similar
                results = vector_db.search_similar(
                    query_embedding=dummy_embedding,
                    project_id=project_id,
                    limit=100,  # Limit results for performance
                    filters={}
                )
                
                if results:
                    # Find the document by name and get its content_text
                    document_found = False
                    for result in results:
                        result_doc_name = result.get("document_name", "") or result.get("source", "")
                        
                        # Check if this result matches our target document
                        if doc_name == result_doc_name or doc_name in result_doc_name:
                            # Get the full content from content_text field
                            full_content = result.get("content_text", "")
                            
                            if full_content and full_content.strip():
                                documents_content.append({
                                    "document_name": doc_name,
                                    "content": full_content,
                                    "character_count": len(full_content),
                                    "source": "content_text_field"
                                })
                                
                                logger.info(f"✅ Retrieved content for {doc_name} ({len(full_content)} chars)")
                                document_found = True
                                break
                            else:
                                logger.warning(f"⚠️ Empty content_text field for document: {doc_name}")
                    
                    if not document_found:
                        logger.warning(f"⚠️ Document not found in results: {doc_name}")
                else:
                    logger.warning(f"⚠️ No search results for document: {doc_name}")
                    
            except Exception as doc_error:
                logger.error(f"❌ Error retrieving content for {doc_name}: {doc_error}")
                continue
        
        return {
            "success": True,
            "documents": documents_content,
            "total_retrieved": len(documents_content),
            "requested_count": len(limited_docs),
            "project_id": project_id
        }
        
    except Exception as e:
        logger.error(f"❌ Error in document content retrieval: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "documents": [],
            "total_retrieved": 0
        }


# Create the Function tool
retrieve_document_content = Function(
    name="retrieve_document_content",
    description="""
    Retrieve full content from multiple documents for detailed analysis.
    
    Use this tool when you need to analyze the actual content of documents 
    found by search_documents_vector. This tool retrieves the complete 
    text content from the content_text field in the Milvus database.
    
    CRITICAL: Only use this tool AFTER you have found documents using search_documents_vector.
    The document_names should come from the search results.
    
    This tool will get the FULL CONTENT of each document, allowing comprehensive 
    analysis of ALL found documents instead of just metadata.
    """,
    parameters={
        "document_names": {
            "type": "array",
            "items": {"type": "string"},
            "description": "List of document names to retrieve content for (from search results)"
        },
        "project_id": {
            "type": "string", 
            "description": "Project ID to filter documents"
        },
        "max_documents": {
            "type": "integer",
            "default": 10,
            "description": "Maximum number of documents to retrieve (default: 10)"
        }
    },
    required=["document_names", "project_id"],
    function_call=_retrieve_document_content_impl
)