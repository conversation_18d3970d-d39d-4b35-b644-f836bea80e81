import re
from datetime import datetime, timedelta
from typing import Dict, Any
from agno.tools.function import Function
import logging

from ..hooks.tool_hooks import standard_pre_hook, standard_post_hook

logger = logging.getLogger(__name__)

def _parse_site_diary_dates_impl(query: str) -> Dict[str, Any]:
    """
    Parse date ranges from site diary search queries with comprehensive pattern matching.
    
    Args:
        query: User's search query (e.g., "find me site diary from last week")
    
    Returns:
        Dictionary with parsed date information:
        - has_date_range: bool - whether a valid date range was found
        - start_date: str - ISO format start date
        - end_date: str - ISO format end date
        - needs_clarification: bool - whether user needs to specify dates
        - clarification_message: str - message for date clarification
        - query_type: str - type of date query detected
    """
    
    query_lower = query.lower().strip()
    today = datetime.now()
    
    # Validation: Check if query is about site diaries
    if not any(keyword in query_lower for keyword in ['site diary', 'diary', 'diaries']):
        return {
            'has_date_range': False,
            'needs_clarification': True,
            'clarification_message': 'This tool handles site diary searches only. Please specify your site diary request.',
            'query_type': 'invalid_request'
        }
    
    # Pattern 1: Specific date range (YYYY-MM-DD to YYYY-MM-DD)
    date_range_pattern = r'(\d{4}-\d{2}-\d{2})\s*(?:to|until|-)\s*(\d{4}-\d{2}-\d{2})'
    date_range_match = re.search(date_range_pattern, query)
    
    if date_range_match:
        start_date_str = date_range_match.group(1)
        end_date_str = date_range_match.group(2)
        
        try:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
            end_date = end_date.replace(hour=23, minute=59, second=59)
            
            return {
                'has_date_range': True,
                'start_date': start_date.isoformat() + '+00:00',
                'end_date': end_date.isoformat() + '+00:00',
                'needs_clarification': False,
                'query_type': 'specific_range'
            }
        except ValueError:
            return {
                'has_date_range': False,
                'needs_clarification': True,
                'clarification_message': 'Invalid date format. Please use YYYY-MM-DD format (e.g., 2025-08-01 to 2025-08-15).',
                'query_type': 'invalid_date_format'
            }
    
    # Pattern 2: Month/Year (e.g., "august 2025", "march 2024")
    month_year_pattern = r'(january|february|march|april|may|june|july|august|september|october|november|december)\s+(\d{4})'
    month_year_match = re.search(month_year_pattern, query_lower)
    
    if month_year_match:
        month_name = month_year_match.group(1)
        year = int(month_year_match.group(2))
        
        month_mapping = {
            'january': 1, 'february': 2, 'march': 3, 'april': 4,
            'may': 5, 'june': 6, 'july': 7, 'august': 8,
            'september': 9, 'october': 10, 'november': 11, 'december': 12
        }
        
        month_num = month_mapping[month_name]
        start_date = datetime(year, month_num, 1)
        
        # Last day of the month
        if month_num == 12:
            end_date = datetime(year + 1, 1, 1) - timedelta(days=1)
        else:
            end_date = datetime(year, month_num + 1, 1) - timedelta(days=1)
        
        end_date = end_date.replace(hour=23, minute=59, second=59)
        
        return {
            'has_date_range': True,
            'start_date': start_date.isoformat() + '+00:00',
            'end_date': end_date.isoformat() + '+00:00',
            'needs_clarification': False,
            'query_type': 'month_year'
        }
    
    # Pattern 3: Last week
    if 'last week' in query_lower:
        # More flexible "last week" - include documents from the past 14 days to capture more results
        last_week_end = today.replace(hour=23, minute=59, second=59)
        last_week_start = today - timedelta(days=14)  # Expand to 14 days to catch more documents
        
        return {
            'has_date_range': True,
            'start_date': last_week_start.isoformat() + '+00:00',
            'end_date': last_week_end.isoformat() + '+00:00',
            'needs_clarification': False,
            'query_type': 'last_week'
        }
    
    # Pattern 4: This week
    if 'this week' in query_lower:
        this_week_start = today - timedelta(days=today.weekday())
        this_week_end = today.replace(hour=23, minute=59, second=59)
        
        return {
            'has_date_range': True,
            'start_date': this_week_start.isoformat() + '+00:00',
            'end_date': this_week_end.isoformat() + '+00:00',
            'needs_clarification': False,
            'query_type': 'this_week'
        }
    
    # Pattern 5: Last month
    if 'last month' in query_lower:
        first_day_this_month = today.replace(day=1)
        last_day_last_month = first_day_this_month - timedelta(days=1)
        first_day_last_month = last_day_last_month.replace(day=1)
        
        return {
            'has_date_range': True,
            'start_date': first_day_last_month.isoformat() + '+00:00',
            'end_date': last_day_last_month.replace(hour=23, minute=59, second=59).isoformat() + '+00:00',
            'needs_clarification': False,
            'query_type': 'last_month'
        }
    
    # Pattern 6: Single date (YYYY-MM-DD)
    single_date_pattern = r'(\d{4}-\d{2}-\d{2})'
    single_date_match = re.search(single_date_pattern, query)
    
    if single_date_match:
        date_str = single_date_match.group(1)
        try:
            target_date = datetime.strptime(date_str, '%Y-%m-%d')
            end_date = target_date.replace(hour=23, minute=59, second=59)
            
            return {
                'has_date_range': True,
                'start_date': target_date.isoformat() + '+00:00',
                'end_date': end_date.isoformat() + '+00:00',
                'needs_clarification': False,
                'query_type': 'single_date'
            }
        except ValueError:
            return {
                'has_date_range': False,
                'needs_clarification': True,
                'clarification_message': 'Invalid date format. Please use YYYY-MM-DD format (e.g., 2025-08-15).',
                'query_type': 'invalid_date_format'
            }
    
    # Pattern 7: Recent/latest (default to last 7 days)
    if any(keyword in query_lower for keyword in ['recent', 'latest', 'last few']):
        seven_days_ago = today - timedelta(days=7)
        
        return {
            'has_date_range': True,
            'start_date': seven_days_ago.isoformat() + '+00:00',
            'end_date': today.replace(hour=23, minute=59, second=59).isoformat() + '+00:00',
            'needs_clarification': False,
            'query_type': 'recent'
        }
    
    # No date range found - needs clarification
    return {
        'has_date_range': False,
        'needs_clarification': True,
        'clarification_message': '''Please specify the date range for your site diary search:

**Examples:**
- "Find me site diary from 2025-08-01 to 2025-08-15" (specific range)
- "Find me site diary for August 2025" (full month)
- "Find me site diary from last week" (last week)
- "Find me site diary from this week" (current week)
- "Find me site diary from last month" (previous month)
- "Find me recent site diary" (last 7 days)

**Date Format:** Use YYYY-MM-DD for specific dates.''',
        'query_type': 'no_date_specified'
    }

# Create the Function object with explicit parameter schema
parse_site_diary_dates = Function(
    name="parse_site_diary_dates",
    entrypoint=_parse_site_diary_dates_impl,
    description="""Parse date ranges from site diary search queries with comprehensive pattern matching.
    
This tool extracts time periods from natural language queries about site diaries and converts them 
to structured date ranges for document searching.""",
    parameters={
        "type": "object",
        "properties": {
            "query": {
                "type": "string",
                "description": "User's search query (e.g., 'find me site diary from last week', 'analyse site diary for last week')"
            }
        },
        "required": ["query"]
    },
    pre_hook=standard_pre_hook,
    post_hook=standard_post_hook,
    show_result=False
)
