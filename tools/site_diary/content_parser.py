# tools/site_diary/content_parser.py
# Malaysian site diary content parser for accurate data extraction
# Provides structured parsing of site diary content_text fields
# RELEVANT FILES: agents/document_analysis_agent.py, tools/vector_db/vector_search_tools.py

import re
from typing import Dict, Any, List, Optional
from agno.tools.function import Function
import logging

from ..hooks.tool_hooks import standard_pre_hook, standard_post_hook

logger = logging.getLogger(__name__)

def _parse_site_diary_content_impl(content_text: str, document_name: str = "") -> Dict[str, Any]:
    """
    Parse Malaysian site diary content to extract structured data accurately.
    
    This tool extracts specific data from site diary content_text fields using
    Malaysian construction diary format patterns.
    
    Args:
        content_text: Raw content text from site diary document
        document_name: Name of the document for reference (optional)
    
    Returns:
        Dictionary with extracted structured data:
        - date: Extracted date from TTARIKH field
        - weather: Weather conditions from CUACA field
        - workers: Worker count and breakdown
        - equipment: Equipment list with hours
        - materials: Materials with quantities
        - activities: Work activities with time ranges
        - delays: Any delays or issues reported
        - parsing_success: <PERSON><PERSON>an indicating if parsing was successful
    """
    
    if not content_text or not isinstance(content_text, str):
        return {
            'parsing_success': False,
            'error': 'Invalid or empty content_text provided',
            'document_name': document_name
        }
    
    result = {
        'document_name': document_name,
        'parsing_success': True,
        'date': None,
        'weather': None,
        'workers': {'total': 0, 'breakdown': [], 'details': 'Not found'},
        'equipment': [],
        'materials': [],
        'activities': [],
        'delays': [],
        'raw_sections': {}
    }
    
    try:
        # 1. Extract Date (TTARIKH)
        date_patterns = [
            r'TTARIKH\s*:\s*(\d{1,2}/\d{1,2}/\d{4})',
            r'TARIKH\s*:\s*(\d{1,2}/\d{1,2}/\d{4})',
            r'(\d{1,2}/\d{1,2}/\d{4})\s*\([^)]+\)'
        ]
        
        for pattern in date_patterns:
            date_match = re.search(pattern, content_text, re.IGNORECASE)
            if date_match:
                result['date'] = date_match.group(1)
                break
        
        # 2. Extract Weather (CUACA)
        weather_patterns = [
            r'CUACA:\s*(ELOK|HUJAN|MENDUNG|PANAS)',
            r'CUACA\s*:\s*([^(\n]+?)(?:\s*\(|$|\n)'
        ]
        
        for pattern in weather_patterns:
            weather_match = re.search(pattern, content_text, re.IGNORECASE)
            if weather_match:
                weather_text = weather_match.group(1).strip()
                if weather_text.upper() in ['ELOK', 'CLEAR']:
                    result['weather'] = 'Clear'
                elif weather_text.upper() in ['HUJAN', 'RAIN']:
                    result['weather'] = 'Rain'
                else:
                    result['weather'] = weather_text
                break
        
        # 3. Extract Worker Counts
        # Look for "BILANGAN PEKERJA" sections and "Jumlah" totals
        worker_section_match = re.search(r'BILANGAN PEKERJA.*?(?=\n\n|\n[A-Z]|$)', content_text, re.DOTALL | re.IGNORECASE)
        if worker_section_match:
            worker_section = worker_section_match.group(0)
            result['raw_sections']['workers'] = worker_section
            
            # Look for Jumlah (total) values
            jumlah_matches = re.findall(r'Jumlah[|\s]*(\d+)', worker_section, re.IGNORECASE)
            if jumlah_matches:
                # Take the highest Jumlah value as total workers
                worker_totals = [int(match) for match in jumlah_matches]
                result['workers']['total'] = max(worker_totals)
                result['workers']['details'] = f"Found Jumlah values: {worker_totals}"
            
            # Extract specific worker roles and counts
            role_patterns = [
                r'(\d+)\s*\|\s*([^|]+?)(?:\s*\||\s*$)',
                r'([^|]+?)\s*[|\s]*(\d+)(?:\s*[|\s]|$)'
            ]
            
            for pattern in role_patterns:
                role_matches = re.findall(pattern, worker_section)
                for match in role_matches:
                    if match[0].isdigit():
                        count, role = int(match[0]), match[1].strip()
                    elif match[1].isdigit():
                        role, count = match[0].strip(), int(match[1])
                    else:
                        continue
                    
                    if count > 0 and len(role) > 2:  # Valid role
                        result['workers']['breakdown'].append({
                            'role': role,
                            'count': count
                        })
        
        # 4. Extract Equipment
        equipment_section_match = re.search(r'LOJI,?\s*ALAT\s*DAN\s*KELENGKAPAN.*?(?=\n\n|\n[A-Z]|$)', content_text, re.DOTALL | re.IGNORECASE)
        if equipment_section_match:
            equipment_section = equipment_section_match.group(0)
            result['raw_sections']['equipment'] = equipment_section
            
            # Extract equipment with IDs and hours
            equipment_patterns = [
                r'([A-Za-z\s]+):\s*([A-Z0-9,-]+).*?(\d+)(?:\s*(?:hours?|jam))?',
                r'([A-Za-z\s]+):\s*([A-Z0-9,-]+)',
                r'(\w+(?:\s+\w+)*)\s*[:\s]\s*([A-Z0-9,-]+)'
            ]
            
            for pattern in equipment_patterns:
                equipment_matches = re.findall(pattern, equipment_section, re.IGNORECASE)
                for match in equipment_matches:
                    equipment_name = match[0].strip()
                    equipment_ids = match[1].strip()
                    hours = match[2] if len(match) > 2 and match[2].isdigit() else None
                    
                    if equipment_name and equipment_ids:
                        result['equipment'].append({
                            'name': equipment_name,
                            'ids': equipment_ids,
                            'hours': int(hours) if hours else None
                        })
        
        # 5. Extract Materials
        materials_section_match = re.search(r'BAHAN-BAHAN\s*BINAAN.*?(?=\n\n|\n[A-Z]|$)', content_text, re.DOTALL | re.IGNORECASE)
        if materials_section_match:
            materials_section = materials_section_match.group(0)
            result['raw_sections']['materials'] = materials_section
            
            # Extract materials with quantities
            material_patterns = [
                r'([A-Za-z\s]+)\s+(\d+)\s*(Load|Bags?|m3|kg|tonnes?)',
                r'([A-Za-z\s]+)\s+(\d+)\s*([A-Za-z]+)\s*(\d+)'
            ]
            
            for pattern in material_patterns:
                material_matches = re.findall(pattern, materials_section, re.IGNORECASE)
                for match in material_matches:
                    material_name = match[0].strip()
                    quantity = match[1]
                    unit = match[2] if len(match) > 2 else ''
                    
                    if material_name and quantity.isdigit():
                        result['materials'].append({
                            'name': material_name,
                            'quantity': int(quantity),
                            'unit': unit
                        })
        
        # 6. Extract Activities
        activities_section_match = re.search(r'KERJA\s*YANG\s*DIBINA.*?(?=\n\n|\n[A-Z]|$)', content_text, re.DOTALL | re.IGNORECASE)
        if activities_section_match:
            activities_section = activities_section_match.group(0)
            result['raw_sections']['activities'] = activities_section
            
            # Extract activities with time ranges
            activity_patterns = [
                r'([A-Za-z\s]+(?:preparation|work|monitoring).*?)\s*(\d{1,2}[:.]\d{2}[AP]M)?\s*-?\s*(\d{1,2}[:.]\d{2}[AP]M)?',
                r'([A-Za-z\s]+)\s*[|\s]*([A-Z0-9\s-]+)\s*[|\s]*(\d{1,2}[:.]\d{2}[AP]M)?'
            ]
            
            for pattern in activity_patterns:
                activity_matches = re.findall(pattern, activities_section, re.IGNORECASE)
                for match in activity_matches:
                    activity_name = match[0].strip()
                    start_time = match[1] if len(match) > 1 and match[1] else None
                    end_time = match[2] if len(match) > 2 and match[2] else None
                    
                    if activity_name and len(activity_name) > 3:
                        result['activities'].append({
                            'name': activity_name,
                            'start_time': start_time,
                            'end_time': end_time
                        })
        
        # 7. Extract Delays
        delays_section_match = re.search(r'KERJA\s*YANG\s*TERGENDALA.*?(?=\n\n|\n[A-Z]|$)', content_text, re.DOTALL | re.IGNORECASE)
        if delays_section_match:
            delays_section = delays_section_match.group(0)
            result['raw_sections']['delays'] = delays_section
            
            # Extract delay reasons
            delay_patterns = [
                r'([A-Za-z\s]+(?:Rain|Soggy|Weather|Equipment).*?)(?:\n|$)',
                r'([A-Za-z\s]+)\s*[|\s]*([A-Za-z\s]+(?:Rain|Soggy|Weather).*?)(?:\n|$)'
            ]
            
            for pattern in delay_patterns:
                delay_matches = re.findall(pattern, delays_section, re.IGNORECASE)
                for match in delay_matches:
                    if isinstance(match, tuple):
                        delay_reason = ' - '.join([m.strip() for m in match if m.strip()])
                    else:
                        delay_reason = match.strip()
                    
                    if delay_reason and len(delay_reason) > 5:
                        result['delays'].append(delay_reason)
        
        # Final validation
        if not result['date']:
            result['parsing_success'] = False
            result['error'] = 'Could not extract date from content'
        
        logger.info(f"✅ Parsed site diary content for {document_name}: Date={result['date']}, Workers={result['workers']['total']}, Equipment={len(result['equipment'])}")
        
        return result
        
    except Exception as e:
        logger.error(f"❌ Error parsing site diary content for {document_name}: {e}")
        return {
            'document_name': document_name,
            'parsing_success': False,
            'error': f'Parsing failed: {str(e)}',
            'date': None,
            'weather': None,
            'workers': {'total': 0, 'breakdown': [], 'details': 'Parsing failed'},
            'equipment': [],
            'materials': [],
            'activities': [],
            'delays': []
        }

# Create the Function object with explicit parameter schema
parse_site_diary_content = Function(
    name="parse_site_diary_content",
    entrypoint=_parse_site_diary_content_impl,
    description="""Parse Malaysian site diary content to extract structured data accurately.

This tool extracts specific data from site diary content_text fields using
Malaysian construction diary format patterns.""",
    parameters={
        "type": "object",
        "properties": {
            "content_text": {
                "type": "string",
                "description": "Raw content text from site diary document"
            },
            "document_name": {
                "type": "string",
                "description": "Name of the document for reference (optional)",
                "default": ""
            }
        },
        "required": ["content_text"]
    },
    pre_hook=standard_pre_hook,
    post_hook=standard_post_hook,
    show_result=True
)
