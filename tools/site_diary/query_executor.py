import os
import requests
from datetime import datetime
from typing import Dict, Any
from agno.tools import tool


@tool
def execute_site_diary_query(
    start_date: str,
    end_date: str,
    project_id: str,
    auth_token: str,
    limit: int = 100
) -> Dict[str, Any]:
    """
    Execute optimized GraphQL query for site diary documents.
    
    Args:
        start_date: Start date in ISO format (e.g., "2025-08-01T00:00:00Z")
        end_date: End date in ISO format (e.g., "2025-08-31T23:59:59Z")
        project_id: Project ID for filtering
        auth_token: Bearer token for authentication
        limit: Maximum number of results to return (default: 100)
    
    Returns:
        Dictionary with GraphQL response and execution metadata
    """
    
    # GraphQL query optimized for site diary searches
    query = """
    query GetSiteDiaries(
      $paging: OffsetPaging,
      $filter: ProjectDocumentFilter,
      $sorting: [ProjectDocumentSort!]
    ) {
      projectDocuments(
        paging: $paging,
        filter: $filter,
        sorting: $sorting
      ) {
        pageInfo {
          hasNextPage
          hasPreviousPage
        }
        nodes {
          id
          name
          fileUrl
          category
          addedBy
          submittedAt
          createdAt
          workspaceGroup {
            name
          }
        }
        totalCount
      }
    }
    """
    
    # Variables with proper filtering for site diaries
    variables = {
        "paging": {
            "limit": limit,
            "offset": 0
        },
        "filter": {
            "projectId": {"eq": project_id},
            "workspaceGroup": {"name": {"like": "site diary"}},
            "submittedAt": {
                "between": {
                    "lower": start_date,
                    "upper": end_date
                }
            }
        },
        "sorting": [
            {
                "field": "submittedAt",
                "direction": "DESC"
            }
        ]
    }
    
    # Get endpoint from environment
    endpoint = os.getenv('GRAPHQL_ENDPOINT', 'http://localhost:3000/graphql')
    
    # Prepare headers with authentication
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {auth_token}',
        'project-id': str(project_id)
    }
    
    # Request payload
    payload = {
        'query': query,
        'variables': variables
    }
    
    # Track execution time
    start_time = datetime.now()
    
    try:
        print(f"DEBUG: Executing site diary search for project {project_id}")
        print(f"DEBUG: Date range: {start_date} to {end_date}")
        print(f"DEBUG: Endpoint: {endpoint}")
        
        response = requests.post(endpoint, json=payload, headers=headers, timeout=30)
        response.raise_for_status()
        
        result = response.json()
        execution_time = (datetime.now() - start_time).total_seconds()
        
        print(f"DEBUG: Query executed in {execution_time:.2f}s")
        
        # Extract and process results
        data = result.get('data', {})
        project_documents = data.get('projectDocuments', {})
        documents = project_documents.get('nodes', [])
        total_count = project_documents.get('totalCount', 0)
        
        print(f"DEBUG: Found {total_count} site diary documents")
        
        return {
            'success': True,
            'total_count': total_count,
            'documents': documents,
            'date_range': {
                'start_date': start_date,
                'end_date': end_date
            },
            'project_id': project_id,
            'execution_time': execution_time,
            'has_more': project_documents.get('pageInfo', {}).get('hasNextPage', False),
            'graphql_errors': result.get('errors'),
            'endpoint_used': endpoint
        }
        
    except requests.exceptions.HTTPError as e:
        error_msg = f'HTTP Error {e.response.status_code}: {e.response.text}'
        print(f"DEBUG: HTTP Error: {error_msg}")
        return {
            'success': False,
            'error': error_msg,
            'error_type': 'http_error',
            'total_count': 0,
            'documents': [],
            'execution_time': (datetime.now() - start_time).total_seconds()
        }
        
    except requests.exceptions.ConnectionError as e:
        error_msg = f'Connection failed to {endpoint}: {str(e)}'
        print(f"DEBUG: Connection Error: {error_msg}")
        return {
            'success': False,
            'error': error_msg,
            'error_type': 'connection_error',
            'total_count': 0,
            'documents': [],
            'execution_time': (datetime.now() - start_time).total_seconds(),
            'suggestion': 'Check that the GraphQL server is running and accessible'
        }
        
    except requests.exceptions.RequestException as e:
        error_msg = f'Request failed: {str(e)}'
        print(f"DEBUG: Request Error: {error_msg}")
        return {
            'success': False,
            'error': error_msg,
            'error_type': 'request_error',
            'total_count': 0,
            'documents': [],
            'execution_time': (datetime.now() - start_time).total_seconds()
        }
        
    except Exception as e:
        error_msg = f'Unexpected error: {str(e)}'
        print(f"DEBUG: Unexpected Error: {error_msg}")
        return {
            'success': False,
            'error': error_msg,
            'error_type': 'unexpected_error',
            'total_count': 0,
            'documents': [],
            'execution_time': (datetime.now() - start_time).total_seconds()
        }