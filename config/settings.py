"""
Centralized configuration management.
"""
from typing import Optional, List
from pydantic import Field
from pydantic_settings import BaseSettings
from functools import lru_cache


class DatabaseSettings(BaseSettings):
    """Database configuration settings."""
    host: str = Field(default="localhost", env="DB_HOST")
    port: int = Field(default=5432, env="DB_PORT")
    user: str = Field(default="ai", env="DB_USER")
    password: str = Field(default="ai", env="DB_PASS")
    database: str = Field(default="ai", env="DB_DATABASE")
    
    @property
    def url(self) -> str:
        return f"postgresql://{self.user}:{self.password}@{self.host}:{self.port}/{self.database}"


class MilvusSettings(BaseSettings):
    """Milvus vector database settings."""
    host: str = Field(default="localhost", env="MILVUS_HOST")
    port: int = Field(default=19530, env="MILVUS_PORT")
    collection_name: str = Field(default="project_documents", env="MILVUS_COLLECTION")
    
    @property
    def uri(self) -> str:
        return f"http://{self.host}:{self.port}"


class EmbeddingSettings(BaseSettings):
    """Embedding model settings."""
    model_name: str = Field(default="all-MiniLM-L6-v2", env="EMBEDDING_MODEL")
    dimension: int = Field(default=384, env="EMBEDDING_DIMENSION")
    max_tokens: int = Field(default=256, env="EMBEDDING_MAX_TOKENS")
    chunk_size: int = Field(default=1000, env="CHUNK_SIZE")
    chunk_overlap: int = Field(default=200, env="CHUNK_OVERLAP")


class AzureOpenAISettings(BaseSettings):
    """Azure OpenAI configuration."""
    api_key: str = Field(default="", env="AZURE_OPENAI_API_KEY")
    endpoint: str = Field(default="", env="AZURE_OPENAI_ENDPOINT")
    api_version: str = Field(default="2024-10-21", env="AZURE_OPENAI_API_VERSION")
    deployment: str = Field(default="", env="AZURE_OPENAI_DEPLOYMENT")


class LoggingSettings(BaseSettings):
    """Logging configuration."""
    level: str = Field(default="INFO", env="LOG_LEVEL")
    format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        env="LOG_FORMAT"
    )
    file_path: Optional[str] = Field(default=None, env="LOG_FILE_PATH")
    max_file_size: int = Field(default=10485760, env="LOG_MAX_FILE_SIZE")  # 10MB
    backup_count: int = Field(default=5, env="LOG_BACKUP_COUNT")


class CacheSettings(BaseSettings):
    """Caching configuration."""
    enabled: bool = Field(default=True, env="CACHE_ENABLED")
    ttl_seconds: int = Field(default=3600, env="CACHE_TTL_SECONDS")  # 1 hour
    max_size: int = Field(default=1000, env="CACHE_MAX_SIZE")


class SecuritySettings(BaseSettings):
    """Security and validation settings."""
    max_document_size_mb: int = Field(default=50, env="MAX_DOCUMENT_SIZE_MB")
    allowed_file_types: List[str] = Field(
        default=["pdf", "txt", "doc", "docx"],
        env="ALLOWED_FILE_TYPES"
    )
    rate_limit_per_minute: int = Field(default=60, env="RATE_LIMIT_PER_MINUTE")


class PerformanceSettings(BaseSettings):
    """Performance and monitoring settings."""
    slow_query_threshold_seconds: float = Field(default=2.0, env="SLOW_QUERY_THRESHOLD")
    vector_search_timeout_seconds: int = Field(default=30, env="VECTOR_SEARCH_TIMEOUT")
    max_concurrent_requests: int = Field(default=10, env="MAX_CONCURRENT_REQUESTS")


class AppSettings(BaseSettings):
    """Main application settings."""
    environment: str = Field(default="development", env="ENVIRONMENT")
    debug: bool = Field(default=True, env="DEBUG")
    host: str = Field(default="0.0.0.0", env="HOST")
    port: int = Field(default=8000, env="PORT")
    
    # Sub-configurations
    database: DatabaseSettings = DatabaseSettings()
    milvus: MilvusSettings = MilvusSettings()
    embedding: EmbeddingSettings = EmbeddingSettings()
    azure_openai: AzureOpenAISettings = AzureOpenAISettings()
    logging: LoggingSettings = LoggingSettings()
    cache: CacheSettings = CacheSettings()
    security: SecuritySettings = SecuritySettings()
    performance: PerformanceSettings = PerformanceSettings()
    
    class Config:
        env_file = ".env"
        case_sensitive = False
        extra = "ignore"  # Ignore extra environment variables


@lru_cache()
def get_settings() -> AppSettings:
    """Get cached application settings."""
    return AppSettings()


# Convenience functions
def get_db_url() -> str:
    """Get database URL."""
    return get_settings().database.url


def get_milvus_uri() -> str:
    """Get Milvus URI."""
    return get_settings().milvus.uri


def is_production() -> bool:
    """Check if running in production."""
    return get_settings().environment.lower() == "production"


def is_debug_enabled() -> bool:
    """Check if debug mode is enabled."""
    return get_settings().debug