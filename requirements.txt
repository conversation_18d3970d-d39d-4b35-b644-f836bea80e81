# This file was cleaned by removing unused dependencies
# Core framework dependencies for Agno AI Agent API

# Core Agno AI framework
agno==1.4.6
fastapi==0.116.1
fastapi-cli==0.0.8
uvicorn==0.35.0
starlette==0.47.2

# Pydantic and validation
pydantic==2.11.7
pydantic-core==2.33.2
pydantic-settings==2.10.1
email-validator==2.2.0
annotated-types==0.7.0
typing-extensions==4.14.1

# HTTP and networking
httpx==0.28.1
httpcore==1.0.9
requests==2.32.5
aiohttp==3.12.15
aiosignal==1.4.0
anyio==4.10.0
h11==0.16.0
httptools==0.6.4
websockets==15.0.1
aiohappyeyeballs==2.6.1

# AI and ML core (keeping numpy for sentence-transformers)
openai==1.101.0
azure-ai-inference==1.0.0b9
azure-core==1.35.0
sentence-transformers==5.1.0
torch==2.8.0
transformers==4.55.4
tokenizers==0.21.4
tiktoken==0.11.0
huggingface-hub==0.34.4
safetensors==0.6.2
numpy==1.26.4

# Vector database
pymilvus==2.6.0
milvus-lite==2.5.1

# Database
sqlalchemy==2.0.43
psycopg==3.2.9
psycopg-binary==3.2.9
pgvector==0.4.1
alembic==1.16.4

# Document processing (unstructured and essential dependencies)
unstructured==0.16.11
unstructured-client==0.42.3
unstructured-inference==0.8.1
unstructured-pytesseract==0.3.15
pdf2image==1.17.0
pikepdf==9.10.2
pypdf==6.0.0
pdfminer-six==20240706
pdfplumber==0.5.3
pillow==11.3.0
wand==0.6.13
filetype==1.2.0
python-magic==0.4.27
pi_heif==1.1.0

# Essential utilities
python-dotenv==1.1.1
python-multipart==0.0.20
aiofiles==24.1.0
click==8.2.1
typer==0.16.1
rich==14.1.0
coloredlogs==15.0.1
humanfriendly==10.0
jinja2==3.1.6
markupsafe==3.0.2

# Security
cryptography==45.0.7

# JSON and serialization  
ujson==5.11.0
jiter==0.10.0

# Text processing
regex==2025.7.34
chardet==5.2.0
charset-normalizer==3.4.3

# Date and time
python-dateutil==2.9.0.post0
pytz==2025.2
tzdata==2025.2

# System utilities
psutil==7.0.0
filelock==3.19.1
portalocker==3.2.0
emoji==2.14.1

# Networking
idna==3.10
certifi==2025.8.3
urllib3==2.5.0
dnspython==2.7.0

# Async
uvloop==0.21.0

# Markdown and text
markdown-it-py==4.0.0
mdurl==0.1.2
pygments==2.19.2

# Configuration
pyyaml==6.0.2
tomli==2.2.1

# Monitoring
sentry-sdk==2.35.0
logfire==4.7.0

# Development
watchfiles==1.1.0
shellingham==1.5.4

# Core Python libraries
setuptools==80.9.0
packaging==25.0
six==1.17.0

# Required indirect dependencies
frozenlist==1.7.0
multidict==6.6.4
propcache==0.3.2
yarl==1.20.1
sniffio==1.3.1
mako==1.3.10
wrapt==1.16.0
