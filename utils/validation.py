"""
Input validation utilities.
"""
import re
from typing import Any, Dict, List, Optional
from urllib.parse import urlparse
from pydantic import ValidationError
import logging

from config.settings import get_settings

logger = logging.getLogger(__name__)

class ValidationResult:
    """Result of validation operations."""
    
    def __init__(self, is_valid: bool, errors: List[str] = None, warnings: List[str] = None):
        self.is_valid = is_valid
        self.errors = errors or []
        self.warnings = warnings or []
    
    def add_error(self, error: str):
        """Add an error message."""
        self.errors.append(error)
        self.is_valid = False
    
    def add_warning(self, warning: str):
        """Add a warning message."""
        self.warnings.append(warning)

class DocumentValidator:
    """Validator for document-related inputs."""
    
    def __init__(self):
        self.settings = get_settings()
    
    def validate_url(self, url: str) -> ValidationResult:
        """
        Validate document URL.
        
        Args:
            url: URL to validate
            
        Returns:
            ValidationResult with validation status
        """
        result = ValidationResult(True)
        
        if not url or not url.strip():
            result.add_error("URL is required")
            return result
        
        # Parse URL
        try:
            parsed = urlparse(url.strip())
        except Exception as e:
            result.add_error(f"Invalid URL format: {str(e)}")
            return result
        
        # Check scheme
        if not parsed.scheme:
            result.add_error("URL must include protocol (http:// or https://)")
            return result
        
        if parsed.scheme.lower() not in ['http', 'https']:
            result.add_error("URL must use HTTP or HTTPS protocol")
            return result
        
        # Check domain
        if not parsed.netloc:
            result.add_error("URL must include domain")
            return result
        
        # Check for suspicious patterns
        suspicious_patterns = [
            r'localhost',
            r'127\.0\.0\.1',
            r'0\.0\.0\.0',
            r'192\.168\.',
            r'10\.',
            r'172\.(1[6-9]|2[0-9]|3[0-1])\.'
        ]
        
        for pattern in suspicious_patterns:
            if re.search(pattern, parsed.netloc, re.IGNORECASE):
                result.add_warning(f"URL appears to reference local/internal network: {parsed.netloc}")
                break
        
        return result
    
    def validate_file_name(self, file_name: str) -> ValidationResult:
        """
        Validate file name.
        
        Args:
            file_name: File name to validate
            
        Returns:
            ValidationResult with validation status
        """
        result = ValidationResult(True)
        
        if not file_name or not file_name.strip():
            result.add_error("File name is required")
            return result
        
        file_name = file_name.strip()
        
        # Check length
        if len(file_name) > 255:
            result.add_error("File name too long (max 255 characters)")
            return result
        
        # Check for invalid characters
        invalid_chars = r'[<>:"/\\|?*\x00-\x1f]'
        if re.search(invalid_chars, file_name):
            result.add_error("File name contains invalid characters")
            return result
        
        # Check file extension
        if '.' in file_name:
            extension = file_name.split('.')[-1].lower()
            if extension not in self.settings.security.allowed_file_types:
                result.add_warning(f"File extension '{extension}' may not be supported")
        else:
            result.add_warning("File has no extension")
        
        return result
    
    def validate_document_id(self, doc_id: str) -> ValidationResult:
        """
        Validate document ID.
        
        Args:
            doc_id: Document ID to validate
            
        Returns:
            ValidationResult with validation status
        """
        result = ValidationResult(True)
        
        if not doc_id or not doc_id.strip():
            result.add_error("Document ID is required")
            return result
        
        doc_id = doc_id.strip()
        
        # Check length
        if len(doc_id) < 1:
            result.add_error("Document ID too short")
            return result
        
        if len(doc_id) > 100:
            result.add_error("Document ID too long (max 100 characters)")
            return result
        
        # Check format (alphanumeric, hyphens, underscores allowed)
        if not re.match(r'^[a-zA-Z0-9_-]+$', doc_id):
            result.add_error("Document ID can only contain letters, numbers, hyphens, and underscores")
            return result
        
        return result
    
    def validate_project_id(self, project_id: str) -> ValidationResult:
        """
        Validate project ID.
        
        Args:
            project_id: Project ID to validate
            
        Returns:
            ValidationResult with validation status
        """
        result = ValidationResult(True)
        
        if not project_id or not project_id.strip():
            result.add_error("Project ID is required")
            return result
        
        project_id = project_id.strip()
        
        # Check length
        if len(project_id) < 1:
            result.add_error("Project ID too short")
            return result
        
        if len(project_id) > 50:
            result.add_error("Project ID too long (max 50 characters)")
            return result
        
        # Check format
        if not re.match(r'^[a-zA-Z0-9_-]+$', project_id):
            result.add_error("Project ID can only contain letters, numbers, hyphens, and underscores")
            return result
        
        return result
    
    def validate_category(self, category: str) -> ValidationResult:
        """
        Validate document category.
        
        Args:
            category: Category to validate
            
        Returns:
            ValidationResult with validation status
        """
        result = ValidationResult(True)
        
        if not category or not category.strip():
            result.add_error("Category is required")
            return result
        
        category = category.strip()
        
        # Check length
        if len(category) > 100:
            result.add_error("Category too long (max 100 characters)")
            return result
        
        # Valid categories for construction industry
        valid_categories = [
            'safety_manual', 'site_diary', 'progress_report', 'inspection_report',
            'quality_report', 'material_specification', 'drawing', 'blueprint',
            'contract', 'permit', 'correspondence', 'invoice', 'general'
        ]
        
        if category.lower() not in valid_categories:
            result.add_warning(f"Category '{category}' is not in standard list: {valid_categories}")
        
        return result

class ChatValidator:
    """Validator for chat and query inputs."""
    
    def validate_query(self, query: str) -> ValidationResult:
        """
        Validate user query.
        
        Args:
            query: User query to validate
            
        Returns:
            ValidationResult with validation status
        """
        result = ValidationResult(True)
        
        if not query or not query.strip():
            result.add_error("Query is required")
            return result
        
        query = query.strip()
        
        # Check length
        if len(query) < 3:
            result.add_error("Query too short (minimum 3 characters)")
            return result
        
        if len(query) > 5000:
            result.add_error("Query too long (maximum 5000 characters)")
            return result
        
        # Check for potential injection attempts
        suspicious_patterns = [
            r'<script[^>]*>',
            r'javascript:',
            r'data:text/html',
            r'SELECT\s+.*FROM',
            r'DROP\s+TABLE',
            r'INSERT\s+INTO',
            r'DELETE\s+FROM'
        ]
        
        for pattern in suspicious_patterns:
            if re.search(pattern, query, re.IGNORECASE):
                result.add_error("Query contains potentially malicious content")
                return result
        
        return result
    
    def validate_session_id(self, session_id: Optional[str]) -> ValidationResult:
        """
        Validate session ID.
        
        Args:
            session_id: Session ID to validate
            
        Returns:
            ValidationResult with validation status
        """
        result = ValidationResult(True)
        
        if session_id is None:
            return result  # Optional field
        
        session_id = session_id.strip() if session_id else ""
        
        if not session_id:
            return result  # Empty is OK
        
        # Check length
        if len(session_id) > 100:
            result.add_error("Session ID too long (max 100 characters)")
            return result
        
        # Check format (alphanumeric, hyphens, underscores allowed)
        if not re.match(r'^[a-zA-Z0-9_-]+$', session_id):
            result.add_error("Session ID can only contain letters, numbers, hyphens, and underscores")
            return result
        
        return result

def validate_pydantic_model(model_class: type, data: Dict[str, Any]) -> ValidationResult:
    """
    Validate data against a Pydantic model.
    
    Args:
        model_class: Pydantic model class
        data: Data to validate
        
    Returns:
        ValidationResult with validation status
    """
    result = ValidationResult(True)
    
    try:
        model_class(**data)
    except ValidationError as e:
        for error in e.errors():
            field = " -> ".join(str(f) for f in error["loc"])
            message = error["msg"]
            result.add_error(f"{field}: {message}")
    except Exception as e:
        result.add_error(f"Validation error: {str(e)}")
    
    return result

def validate_file_size(content_length: Optional[int]) -> ValidationResult:
    """
    Validate file size.
    
    Args:
        content_length: Content length in bytes
        
    Returns:
        ValidationResult with validation status
    """
    result = ValidationResult(True)
    settings = get_settings()
    
    if content_length is None:
        result.add_warning("Could not determine file size")
        return result
    
    max_size_bytes = settings.security.max_document_size_mb * 1024 * 1024
    
    if content_length > max_size_bytes:
        result.add_error(f"File too large ({content_length} bytes). Maximum size: {settings.security.max_document_size_mb}MB")
        return result
    
    if content_length == 0:
        result.add_error("File appears to be empty")
        return result
    
    return result

def sanitize_filename(filename: str) -> str:
    """
    Sanitize filename for safe storage.
    
    Args:
        filename: Original filename
        
    Returns:
        Sanitized filename
    """
    if not filename:
        return "unnamed_file"
    
    # Remove path components
    filename = filename.split('/')[-1].split('\\')[-1]
    
    # Replace invalid characters with underscores
    filename = re.sub(r'[<>:"/\\|?*\x00-\x1f]', '_', filename)
    
    # Limit length
    if len(filename) > 255:
        name_part, ext_part = filename.rsplit('.', 1) if '.' in filename else (filename, '')
        max_name_length = 255 - len(ext_part) - 1 if ext_part else 255
        filename = name_part[:max_name_length] + ('.' + ext_part if ext_part else '')
    
    return filename or "unnamed_file"