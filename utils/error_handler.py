"""
Centralized error handling and resilience patterns.
"""
import logging
from typing import Any, Callable, Dict
from functools import wraps
import time
from contextlib import contextmanager

logger = logging.getLogger(__name__)


class AgentError(Exception):
    """Base exception for agent-related errors."""
    def __init__(self, message: str, error_code: str = None, context: Dict[str, Any] = None):
        self.message = message
        self.error_code = error_code
        self.context = context or {}
        super().__init__(message)


class VectorSearchError(AgentError):
    """Exception for vector search related errors."""
    pass


class DocumentProcessingError(AgentError):
    """Exception for document processing errors."""
    pass


def retry_with_exponential_backoff(
    max_retries: int = 3,
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    exceptions: tuple = (Exception,)
):
    """Decorator for retrying functions with exponential backoff."""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    if attempt == max_retries:
                        logger.error(f"Function {func.__name__} failed after {max_retries} retries: {str(e)}")
                        raise
                    
                    delay = min(base_delay * (2 ** attempt), max_delay)
                    logger.warning(f"Attempt {attempt + 1} failed for {func.__name__}: {str(e)}. Retrying in {delay:.2f}s")
                    time.sleep(delay)
            
            raise last_exception
        return wrapper
    return decorator


@contextmanager
def error_context(operation: str, **context):
    """Context manager for consistent error logging."""
    start_time = time.time()
    logger.info(f"Starting operation: {operation}", extra=context)
    
    try:
        yield
        duration = time.time() - start_time
        logger.info(f"Operation completed: {operation} in {duration:.2f}s", extra=context)
    except Exception as e:
        duration = time.time() - start_time
        logger.error(f"Operation failed: {operation} after {duration:.2f}s - {str(e)}", extra=context)
        raise


def safe_execute(func: Callable, default_return: Any = None, log_errors: bool = True) -> Any:
    """Safely execute a function and return default on error."""
    try:
        return func()
    except Exception as e:
        if log_errors:
            logger.error(f"Safe execution failed for {func.__name__}: {str(e)}")
        return default_return


def validate_required_fields(data: Dict[str, Any], required_fields: list) -> None:
    """Validate that all required fields are present and not empty."""
    missing_fields = []
    empty_fields = []
    
    for field in required_fields:
        if field not in data:
            missing_fields.append(field)
        elif not data[field] or (isinstance(data[field], str) and not data[field].strip()):
            empty_fields.append(field)
    
    if missing_fields or empty_fields:
        error_msg = "Validation failed: "
        if missing_fields:
            error_msg += f"Missing fields: {missing_fields}. "
        if empty_fields:
            error_msg += f"Empty fields: {empty_fields}."
        raise ValueError(error_msg)