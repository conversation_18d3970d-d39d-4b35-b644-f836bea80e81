"""
Deployment and infrastructure utilities.
"""
import os
import logging
from typing import Dict, Any
from pathlib import Path

logger = logging.getLogger(__name__)

class DeploymentHelper:
    """Utilities for deployment and infrastructure management."""
    
    @staticmethod
    def check_environment_variables() -> Dict[str, Any]:
        """
        Check required environment variables.
        
        Returns:
            Dictionary with environment check results
        """
        required_vars = [
            'OPENAI_API_KEY',
            'DB_HOST', 'DB_PORT', 'DB_USER', 'DB_PASS', 'DB_DATABASE',
            'MILVUS_HOST', 'MILVUS_PORT'
        ]
        
        optional_vars = [
            'AGNO_API_KEY', 'AGNO_MONITOR',
            'LOG_LEVEL', 'ENVIRONMENT'
        ]
        
        results = {
            "required": {},
            "optional": {},
            "missing_required": [],
            "all_required_present": True
        }
        
        # Check required variables
        for var in required_vars:
            value = os.getenv(var)
            if value:
                results["required"][var] = "✓ Present"
            else:
                results["required"][var] = "✗ Missing"
                results["missing_required"].append(var)
                results["all_required_present"] = False
        
        # Check optional variables
        for var in optional_vars:
            value = os.getenv(var)
            results["optional"][var] = "✓ Present" if value else "- Not set"
        
        return results
    
    @staticmethod
    def generate_docker_compose_override() -> str:
        """
        Generate docker-compose.override.yml for local development.
        
        Returns:
            Docker compose override content
        """
        return """# docker-compose.override.yml
# Override for local development
version: '3.8'

services:
  api:
    volumes:
      - .:/app
    environment:
      - DEBUG=true
      - LOG_LEVEL=DEBUG
    ports:
      - "8000:8000"
    
  pgvector:
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=ai_dev
      - POSTGRES_USER=ai_dev
      - POSTGRES_PASSWORD=ai_dev
  
  milvus:
    ports:
      - "19530:19530"
  
  attu:
    ports:
      - "8002:3000"
    environment:
      - MILVUS_URL=milvus:19530
"""
    
    @staticmethod
    def generate_production_env_template() -> str:
        """
        Generate .env template for production deployment.
        
        Returns:
            Environment template content
        """
        return """# Production Environment Configuration
# Copy this to .env and fill in the values

# Application Settings
ENVIRONMENT=production
DEBUG=false
HOST=0.0.0.0
PORT=8000

# Database Configuration
DB_HOST=your-postgres-host
DB_PORT=5432
DB_USER=your-db-user
DB_PASS=your-secure-password
DB_DATABASE=ai_production

# Milvus Configuration
MILVUS_HOST=your-milvus-host
MILVUS_PORT=19530
MILVUS_COLLECTION=project_documents_prod

# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key

# Azure OpenAI Configuration (if using Azure)
AZURE_OPENAI_API_KEY=your-azure-openai-key
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_API_VERSION=2024-10-21
AZURE_OPENAI_DEPLOYMENT=your-deployment-name

# Agno Configuration (optional)
AGNO_API_KEY=your-agno-api-key
AGNO_MONITOR=true

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE_PATH=/app/logs/app.log
LOG_MAX_FILE_SIZE=10485760
LOG_BACKUP_COUNT=5

# Security Settings
MAX_DOCUMENT_SIZE_MB=50
RATE_LIMIT_PER_MINUTE=100

# Performance Settings
CACHE_ENABLED=true
CACHE_TTL_SECONDS=3600
CACHE_MAX_SIZE=2000
SLOW_QUERY_THRESHOLD=2.0
VECTOR_SEARCH_TIMEOUT=30
MAX_CONCURRENT_REQUESTS=20

# Embedding Settings
EMBEDDING_MODEL=all-MiniLM-L6-v2
EMBEDDING_DIMENSION=384
EMBEDDING_MAX_TOKENS=256
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
"""
    
    @staticmethod
    def generate_kubernetes_manifests() -> Dict[str, str]:
        """
        Generate Kubernetes deployment manifests.
        
        Returns:
            Dictionary with manifest files
        """
        return {
            "deployment.yaml": """apiVersion: apps/v1
kind: Deployment
metadata:
  name: agno-agent-api
  labels:
    app: agno-agent-api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: agno-agent-api
  template:
    metadata:
      labels:
        app: agno-agent-api
    spec:
      containers:
      - name: api
        image: agno-agent-api:latest
        ports:
        - containerPort: 8000
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: DB_HOST
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: host
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: user
        - name: DB_PASS
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: password
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: openai-secret
              key: api-key
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /v1/health/live
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /v1/health/ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
""",
            "service.yaml": """apiVersion: v1
kind: Service
metadata:
  name: agno-agent-api-service
spec:
  selector:
    app: agno-agent-api
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8000
  type: LoadBalancer
""",
            "configmap.yaml": """apiVersion: v1
kind: ConfigMap
metadata:
  name: agno-agent-config
data:
  LOG_LEVEL: "INFO"
  CACHE_ENABLED: "true"
  CACHE_TTL_SECONDS: "3600"
  MAX_DOCUMENT_SIZE_MB: "50"
  EMBEDDING_MODEL: "all-MiniLM-L6-v2"
""",
            "secrets-template.yaml": """# Create secrets with actual values:
# kubectl create secret generic db-secret \\
#   --from-literal=host=your-db-host \\
#   --from-literal=user=your-db-user \\
#   --from-literal=password=your-db-password
#
# kubectl create secret generic openai-secret \\
#   --from-literal=api-key=your-openai-key
"""
        }
    
    @staticmethod
    def check_dependencies() -> Dict[str, Any]:
        """
        Check if all required dependencies are available.
        
        Returns:
            Dictionary with dependency check results
        """
        dependencies = {
            "python_packages": {},
            "external_services": {}
        }
        
        # Check Python packages
        required_packages = [
            'fastapi', 'uvicorn', 'pydantic', 'psycopg2-binary',
            'pymilvus', 'sentence-transformers', 'agno', 'PyMuPDF',
            'tiktoken', 'httpx'
        ]
        
        for package in required_packages:
            try:
                __import__(package.replace('-', '_'))
                dependencies["python_packages"][package] = "✓ Available"
            except ImportError:
                dependencies["python_packages"][package] = "✗ Missing"
        
        # Check external services (basic connectivity)
        external_checks = {
            "postgres": "Check DB_HOST connection",
            "milvus": "Check MILVUS_HOST connection", 
            "openai": "Check OPENAI_API_KEY validity"
        }
        
        for service, description in external_checks.items():
            dependencies["external_services"][service] = description
        
        return dependencies
    
    @staticmethod
    def generate_health_check_script() -> str:
        """
        Generate health check script for monitoring.
        
        Returns:
            Health check script content
        """
        return """#!/bin/bash
# health-check.sh
# Health check script for monitoring

API_URL="${API_URL:-http://localhost:8000}"
TIMEOUT="${TIMEOUT:-30}"

echo "Health Check starting at $(date)"
echo "API URL: $API_URL"

# Check liveness
echo "Checking liveness..."
LIVENESS=$(curl -s -w "%{http_code}" -o /tmp/liveness.json --connect-timeout $TIMEOUT "$API_URL/v1/health/live")

if [ "$LIVENESS" = "200" ]; then
    echo "✓ Liveness check passed"
else
    echo "✗ Liveness check failed (HTTP $LIVENESS)"
    exit 1
fi

# Check readiness
echo "Checking readiness..."
READINESS=$(curl -s -w "%{http_code}" -o /tmp/readiness.json --connect-timeout $TIMEOUT "$API_URL/v1/health/ready")

if [ "$READINESS" = "200" ]; then
    echo "✓ Readiness check passed"
else
    echo "✗ Readiness check failed (HTTP $READINESS)"
    cat /tmp/readiness.json 2>/dev/null || echo "No response body"
    exit 1
fi

# Check comprehensive health
echo "Checking comprehensive health..."
HEALTH=$(curl -s -w "%{http_code}" -o /tmp/health.json --connect-timeout $TIMEOUT "$API_URL/v1/health/")

if [ "$HEALTH" = "200" ]; then
    echo "✓ Comprehensive health check passed"
    echo "Health details:"
    cat /tmp/health.json | python3 -m json.tool 2>/dev/null || cat /tmp/health.json
else
    echo "✗ Comprehensive health check failed (HTTP $HEALTH)"
    cat /tmp/health.json 2>/dev/null || echo "No response body"
    exit 1
fi

echo "All health checks passed at $(date)"
"""

def create_deployment_directory(base_path: str = "./deployment") -> str:
    """
    Create deployment directory with all necessary files.
    
    Args:
        base_path: Base path for deployment directory
        
    Returns:
        Path to created deployment directory
    """
    deployment_path = Path(base_path)
    deployment_path.mkdir(exist_ok=True)
    
    helper = DeploymentHelper()
    
    # Create subdirectories
    (deployment_path / "kubernetes").mkdir(exist_ok=True)
    (deployment_path / "docker").mkdir(exist_ok=True)
    (deployment_path / "scripts").mkdir(exist_ok=True)
    
    # Generate files
    files_to_create = {
        "docker/docker-compose.override.yml": helper.generate_docker_compose_override(),
        ".env.production.template": helper.generate_production_env_template(),
        "scripts/health-check.sh": helper.generate_health_check_script()
    }
    
    # Create Kubernetes manifests
    k8s_manifests = helper.generate_kubernetes_manifests()
    for filename, content in k8s_manifests.items():
        files_to_create[f"kubernetes/{filename}"] = content
    
    # Write all files
    for relative_path, content in files_to_create.items():
        file_path = deployment_path / relative_path
        file_path.parent.mkdir(parents=True, exist_ok=True)
        file_path.write_text(content)
        
        # Make scripts executable
        if relative_path.startswith("scripts/"):
            file_path.chmod(0o755)
    
    logger.info(f"Deployment directory created at: {deployment_path.absolute()}")
    return str(deployment_path.absolute())