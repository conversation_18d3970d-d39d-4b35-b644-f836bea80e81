"""
Enhanced logging configuration with structured logging.
"""
import logging
import logging.handlers
import json
import sys
from datetime import datetime
from typing import Optional
from config.settings import get_settings


class JSONFormatter(logging.Formatter):
    """JSON formatter for structured logging."""
    
    def format(self, record: logging.LogRecord) -> str:
        log_entry = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
        }
        
        # Add extra fields if present
        if hasattr(record, 'user_id'):
            log_entry['user_id'] = record.user_id
        if hasattr(record, 'session_id'):
            log_entry['session_id'] = record.session_id
        if hasattr(record, 'agent_id'):
            log_entry['agent_id'] = record.agent_id
        if hasattr(record, 'tool_name'):
            log_entry['tool_name'] = record.tool_name
        if hasattr(record, 'duration'):
            log_entry['duration'] = record.duration
        if hasattr(record, 'document_count'):
            log_entry['document_count'] = record.document_count
            
        # Add exception info if present
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
            
        return json.dumps(log_entry)


class AgentContextFilter(logging.Filter):
    """Add agent context to log records."""
    
    def __init__(self, agent_id: Optional[str] = None):
        super().__init__()
        self.agent_id = agent_id
    
    def filter(self, record: logging.LogRecord) -> bool:
        if self.agent_id:
            record.agent_id = self.agent_id
        return True


def setup_logging():
    """Setup application logging with proper configuration."""
    settings = get_settings()
    
    # Create root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, settings.logging.level.upper()))
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    if settings.environment.lower() == "production":
        console_handler.setFormatter(JSONFormatter())
    else:
        console_handler.setFormatter(
            logging.Formatter(settings.logging.format)
        )
    root_logger.addHandler(console_handler)
    
    # File handler (if configured)
    if settings.logging.file_path:
        file_handler = logging.handlers.RotatingFileHandler(
            settings.logging.file_path,
            maxBytes=settings.logging.max_file_size,
            backupCount=settings.logging.backup_count
        )
        file_handler.setFormatter(JSONFormatter())
        root_logger.addHandler(file_handler)
    
    # Set specific logger levels
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("sentence_transformers").setLevel(logging.WARNING)
    logging.getLogger("transformers").setLevel(logging.WARNING)
    
    return root_logger


def get_agent_logger(agent_id: str) -> logging.Logger:
    """Get a logger with agent context."""
    logger = logging.getLogger(f"agent.{agent_id}")
    logger.addFilter(AgentContextFilter(agent_id))
    return logger


def log_tool_execution(
    logger: logging.Logger,
    tool_name: str,
    duration: float,
    success: bool,
    user_id: Optional[str] = None,
    session_id: Optional[str] = None,
    **kwargs
):
    """Log tool execution with structured data."""
    extra_data = {
        'tool_name': tool_name,
        'duration': duration,
        'success': success
    }
    
    if user_id:
        extra_data['user_id'] = user_id
    if session_id:
        extra_data['session_id'] = session_id
        
    extra_data.update(kwargs)
    
    if success:
        logger.info(f"Tool executed successfully: {tool_name}", extra=extra_data)
    else:
        logger.error(f"Tool execution failed: {tool_name}", extra=extra_data)


def log_vector_search(
    logger: logging.Logger,
    query: str,
    result_count: int,
    duration: float,
    success: bool,
    user_id: Optional[str] = None,
    **kwargs
):
    """Log vector search operations."""
    extra_data = {
        'tool_name': 'vector_search',
        'query': query,
        'document_count': result_count,
        'duration': duration,
        'success': success
    }
    
    if user_id:
        extra_data['user_id'] = user_id
        
    extra_data.update(kwargs)
    
    logger.info("Vector search completed", extra=extra_data)