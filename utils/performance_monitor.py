"""
Performance monitoring and optimization utilities.
"""
import time
import logging
import functools
import asyncio
from typing import Callable, Any, Dict, Optional
from datetime import datetime
import psutil
import threading

logger = logging.getLogger(__name__)


class PerformanceMonitor:
    """Monitor and log performance metrics."""
    
    def __init__(self):
        self.metrics = {
            "function_times": {},
            "slow_queries": [],
            "memory_usage": [],
            "cpu_usage": []
        }
        self._lock = threading.Lock()
    
    def record_execution(self, func_name: str, duration: float, success: bool = True):
        """Record function execution metrics."""
        with self._lock:
            if func_name not in self.metrics["function_times"]:
                self.metrics["function_times"][func_name] = {
                    "count": 0,
                    "total_time": 0,
                    "min_time": float('inf'),
                    "max_time": 0,
                    "failures": 0
                }
            
            stats = self.metrics["function_times"][func_name]
            stats["count"] += 1
            
            if success:
                stats["total_time"] += duration
                stats["min_time"] = min(stats["min_time"], duration)
                stats["max_time"] = max(stats["max_time"], duration)
            else:
                stats["failures"] += 1
            
            # Record slow queries
            if duration > 2.0:  # Threshold for slow queries
                self.metrics["slow_queries"].append({
                    "function": func_name,
                    "duration": duration,
                    "timestamp": datetime.now().isoformat()
                })
                
                # Keep only last 100 slow queries
                if len(self.metrics["slow_queries"]) > 100:
                    self.metrics["slow_queries"] = self.metrics["slow_queries"][-100:]
    
    def get_stats(self, func_name: Optional[str] = None) -> Dict[str, Any]:
        """Get performance statistics."""
        with self._lock:
            if func_name:
                stats = self.metrics["function_times"].get(func_name, {})
                if stats and stats.get("count", 0) > 0:
                    avg_time = stats["total_time"] / stats["count"]
                    return {
                        "function": func_name,
                        "count": stats["count"],
                        "average_time": avg_time,
                        "min_time": stats["min_time"],
                        "max_time": stats["max_time"],
                        "failures": stats["failures"],
                        "success_rate": 1 - (stats["failures"] / stats["count"])
                    }
                return {}
            
            # Return all stats
            all_stats = {}
            for name, stats in self.metrics["function_times"].items():
                if stats["count"] > 0:
                    all_stats[name] = {
                        "count": stats["count"],
                        "average_time": stats["total_time"] / stats["count"],
                        "min_time": stats["min_time"],
                        "max_time": stats["max_time"],
                        "failures": stats["failures"]
                    }
            
            return {
                "functions": all_stats,
                "slow_queries": self.metrics["slow_queries"][-10:],  # Last 10
                "system": self.get_system_metrics()
            }
    
    def get_system_metrics(self) -> Dict[str, Any]:
        """Get system performance metrics."""
        try:
            process = psutil.Process()
            return {
                "cpu_percent": process.cpu_percent(interval=0.1),
                "memory_mb": process.memory_info().rss / 1024 / 1024,
                "memory_percent": process.memory_percent(),
                "num_threads": process.num_threads()
            }
        except:
            return {}
    
    def reset_stats(self):
        """Reset all statistics."""
        with self._lock:
            self.metrics = {
                "function_times": {},
                "slow_queries": [],
                "memory_usage": [],
                "cpu_usage": []
            }


# Global performance monitor instance
_monitor = PerformanceMonitor()


def performance_tracked(func: Callable) -> Callable:
    """Decorator to track function performance."""
    
    @functools.wraps(func)
    def sync_wrapper(*args, **kwargs):
        start_time = time.time()
        success = True
        try:
            result = func(*args, **kwargs)
            return result
        except Exception:
            success = False
            raise
        finally:
            duration = time.time() - start_time
            _monitor.record_execution(func.__name__, duration, success)
            
            if duration > 2.0:  # Log slow executions
                logger.warning(f"🐌 SLOW: {func.__name__} took {duration:.2f}s")
            elif duration > 0.5:
                logger.info(f"⚠️ {func.__name__} took {duration:.2f}s")
    
    @functools.wraps(func)
    async def async_wrapper(*args, **kwargs):
        start_time = time.time()
        success = True
        try:
            result = await func(*args, **kwargs)
            return result
        except Exception:
            success = False
            raise
        finally:
            duration = time.time() - start_time
            _monitor.record_execution(func.__name__, duration, success)
            
            if duration > 2.0:  # Log slow executions
                logger.warning(f"🐌 SLOW: {func.__name__} took {duration:.2f}s")
            elif duration > 0.5:
                logger.info(f"⚠️ {func.__name__} took {duration:.2f}s")
    
    # Return appropriate wrapper based on function type
    if asyncio.iscoroutinefunction(func):
        return async_wrapper
    else:
        return sync_wrapper


def get_performance_stats(func_name: Optional[str] = None) -> Dict[str, Any]:
    """Get performance statistics."""
    return _monitor.get_stats(func_name)


def reset_performance_stats():
    """Reset performance statistics."""
    _monitor.reset_stats()


class TimedBlock:
    """Context manager for timing code blocks."""
    
    def __init__(self, name: str, log_slow: bool = True):
        self.name = name
        self.log_slow = log_slow
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        duration = time.time() - self.start_time
        _monitor.record_execution(self.name, duration, success=(exc_type is None))
        
        if self.log_slow:
            if duration > 2.0:
                logger.warning(f"🐌 SLOW BLOCK: {self.name} took {duration:.2f}s")
            elif duration > 0.5:
                logger.info(f"⚠️ {self.name} took {duration:.2f}s")
            else:
                logger.debug(f"✅ {self.name} took {duration:.3f}s")


# Performance optimization tips based on metrics
def analyze_performance() -> Dict[str, Any]:
    """Analyze performance and provide optimization suggestions."""
    stats = get_performance_stats()
    suggestions = []
    
    if not stats or "functions" not in stats:
        return {"status": "No performance data available"}
    
    # Analyze function performance
    for func_name, func_stats in stats["functions"].items():
        avg_time = func_stats.get("average_time", 0)
        
        if avg_time > 5.0:
            suggestions.append({
                "function": func_name,
                "issue": "Very slow execution",
                "average_time": avg_time,
                "suggestion": "Consider caching, parallel processing, or algorithm optimization"
            })
        elif avg_time > 2.0:
            suggestions.append({
                "function": func_name,
                "issue": "Slow execution",
                "average_time": avg_time,
                "suggestion": "Review for optimization opportunities"
            })
        
        # Check failure rate
        failure_rate = 1 - func_stats.get("success_rate", 1)
        if failure_rate > 0.1:  # More than 10% failures
            suggestions.append({
                "function": func_name,
                "issue": f"High failure rate: {failure_rate:.1%}",
                "suggestion": "Investigate error handling and stability"
            })
    
    # System metrics analysis
    system_metrics = stats.get("system", {})
    if system_metrics:
        if system_metrics.get("memory_percent", 0) > 80:
            suggestions.append({
                "issue": "High memory usage",
                "value": f"{system_metrics['memory_percent']:.1f}%",
                "suggestion": "Consider memory optimization or increasing resources"
            })
        
        if system_metrics.get("cpu_percent", 0) > 80:
            suggestions.append({
                "issue": "High CPU usage",
                "value": f"{system_metrics['cpu_percent']:.1f}%",
                "suggestion": "Consider parallel processing or algorithm optimization"
            })
    
    return {
        "statistics": stats,
        "suggestions": suggestions,
        "slow_queries_count": len(stats.get("slow_queries", [])),
        "timestamp": datetime.now().isoformat()
    }