"""
Caching utilities for improving performance.
"""
import hashlib
import json
import logging
from functools import wraps
from typing import Any, Callable, Dict, Optional
from datetime import datetime, timedelta
from threading import Lock

from config.settings import get_settings

logger = logging.getLogger(__name__)

class InMemoryCache:
    """Thread-safe in-memory cache with TTL support."""
    
    def __init__(self, max_size: int = 1000, default_ttl: int = 3600):
        self.cache: Dict[str, Dict] = {}
        self.max_size = max_size
        self.default_ttl = default_ttl
        self._lock = Lock()
    
    def _generate_key(self, prefix: str, *args, **kwargs) -> str:
        """Generate cache key from function arguments."""
        key_data = {
            "prefix": prefix,
            "args": args,
            "kwargs": sorted(kwargs.items())
        }
        key_string = json.dumps(key_data, sort_keys=True, default=str)
        return hashlib.md5(key_string.encode()).hexdigest()
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache if not expired."""
        with self._lock:
            if key not in self.cache:
                return None
            
            entry = self.cache[key]
            if datetime.now() > entry["expires_at"]:
                del self.cache[key]
                return None
                
            # Update access time
            entry["accessed_at"] = datetime.now()
            return entry["value"]
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Set value in cache with TTL."""
        if ttl is None:
            ttl = self.default_ttl
            
        with self._lock:
            # Remove oldest entries if cache is full
            if len(self.cache) >= self.max_size:
                self._evict_oldest()
            
            self.cache[key] = {
                "value": value,
                "created_at": datetime.now(),
                "accessed_at": datetime.now(),
                "expires_at": datetime.now() + timedelta(seconds=ttl)
            }
    
    def delete(self, key: str) -> None:
        """Delete key from cache."""
        with self._lock:
            self.cache.pop(key, None)
    
    def clear(self) -> None:
        """Clear all cache entries."""
        with self._lock:
            self.cache.clear()
    
    def _evict_oldest(self) -> None:
        """Evict oldest accessed entry."""
        if not self.cache:
            return
            
        oldest_key = min(
            self.cache.keys(),
            key=lambda k: self.cache[k]["accessed_at"]
        )
        del self.cache[oldest_key]
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        with self._lock:
            total_entries = len(self.cache)
            expired_count = sum(
                1 for entry in self.cache.values()
                if datetime.now() > entry["expires_at"]
            )
            
            return {
                "total_entries": total_entries,
                "expired_entries": expired_count,
                "active_entries": total_entries - expired_count,
                "max_size": self.max_size,
                "cache_utilization": total_entries / self.max_size if self.max_size > 0 else 0
            }

# Global cache instance
_cache = None

def get_cache() -> InMemoryCache:
    """Get global cache instance."""
    global _cache
    if _cache is None:
        settings = get_settings()
        _cache = InMemoryCache(
            max_size=settings.cache.max_size,
            default_ttl=settings.cache.ttl_seconds
        )
    return _cache

def cached(ttl: Optional[int] = None, key_prefix: str = None):
    """
    Decorator to cache function results.
    
    Args:
        ttl: Time to live in seconds (uses default if None)
        key_prefix: Custom prefix for cache keys
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            settings = get_settings()
            if not settings.cache.enabled:
                return func(*args, **kwargs)
                
            cache = get_cache()
            prefix = key_prefix or f"{func.__module__}.{func.__name__}"
            cache_key = cache._generate_key(prefix, *args, **kwargs)
            
            # Try to get from cache
            cached_result = cache.get(cache_key)
            if cached_result is not None:
                logger.debug(f"Cache hit for {prefix}")
                return cached_result
            
            # Execute function and cache result
            logger.debug(f"Cache miss for {prefix}, executing function")
            result = func(*args, **kwargs)
            cache.set(cache_key, result, ttl)
            
            return result
        return wrapper
    return decorator

def cache_vector_search_results(ttl: int = 300):
    """Cache decorator specifically for vector search results."""
    return cached(ttl=ttl, key_prefix="vector_search")

def cache_document_analysis(ttl: int = 1800):
    """Cache decorator for document analysis results."""
    return cached(ttl=ttl, key_prefix="document_analysis")

def invalidate_cache_pattern(pattern: str) -> int:
    """
    Invalidate cache entries matching pattern.
    
    Args:
        pattern: String pattern to match in keys
        
    Returns:
        Number of entries invalidated
    """
    cache = get_cache()
    with cache._lock:
        keys_to_delete = [
            key for key in cache.cache.keys()
            if pattern in key
        ]
        
        for key in keys_to_delete:
            del cache.cache[key]
            
        logger.info(f"Invalidated {len(keys_to_delete)} cache entries matching pattern: {pattern}")
        return len(keys_to_delete)