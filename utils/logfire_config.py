"""
Logfire configuration and instrumentation setup for the agent API.
Provides centralized monitoring, performance tracking, and observability.
"""
import os
import logging
from typing import Optional
from contextlib import asynccontextmanager

logger = logging.getLogger(__name__)

# Global logfire instance
_logfire_instance = None

def get_logfire():
    """Get the global logfire instance."""
    global _logfire_instance
    return _logfire_instance

def is_logfire_enabled() -> bool:
    """Check if Logfire is enabled based on environment variables."""
    disabled = os.getenv("LOGFIRE_DISABLED", "false").lower() == "true"
    token_available = bool(os.getenv("LOGFIRE_TOKEN"))
    return not disabled and token_available

def initialize_logfire() -> bool:
    """Initialize Logfire with configuration from environment variables."""
    global _logfire_instance
    
    if not is_logfire_enabled():
        logger.info("🔧 Logfire is disabled or token not available")
        return False
    
    try:
        import logfire
        
        # Get configuration from environment
        token = os.getenv("LOGFIRE_TOKEN")
        project_name = os.getenv("LOGFIRE_PROJECT_NAME", "bina-agent-api")
        service_name = os.getenv("LOGFIRE_SERVICE_NAME", "agent-api")
        environment = os.getenv("LOGFIRE_ENVIRONMENT", "production")
        
        if not token:
            logger.warning("⚠️ LOGFIRE_TOKEN not set, skipping Logfire initialization")
            return False
        
        # Configure Logfire
        logfire.configure(
            token=token,
            project_name=project_name,
            service_name=service_name,
            environment=environment,
            send_to_logfire=True,
            console=False,  # Disable console output to avoid noise
        )
        
        _logfire_instance = logfire
        
        logger.info(f"✅ Logfire initialized successfully")
        logger.info(f"   - Project: {project_name}")
        logger.info(f"   - Service: {service_name}")
        logger.info(f"   - Environment: {environment}")
        
        return True
        
    except ImportError:
        logger.warning("⚠️ Logfire not installed, monitoring disabled")
        return False
    except Exception as e:
        logger.error(f"❌ Failed to initialize Logfire: {e}")
        return False

def instrument_fastapi(app):
    """Add Logfire instrumentation to FastAPI application."""
    if not is_logfire_enabled():
        return
    
    try:
        logfire = get_logfire()
        if logfire:
            logfire.instrument_fastapi(app)
            logfire.instrument_httpx()
            logfire.instrument_psycopg()
            logger.info("🎯 FastAPI instrumentation enabled")
    except Exception as e:
        logger.warning(f"⚠️ Failed to add FastAPI instrumentation: {e}")

@asynccontextmanager
async def logfire_span(name: str, **kwargs):
    """Create a Logfire span with automatic error handling."""
    logfire = get_logfire()
    if not logfire:
        # No-op context manager if Logfire not available
        yield None
        return
    
    try:
        with logfire.span(name, **kwargs) as span:
            yield span
    except Exception as e:
        logger.debug(f"Error in logfire span '{name}': {e}")
        yield None

def log_agent_interaction(agent_id: str, user_id: str, project_id: Optional[str], 
                         message: str, response_time_ms: int, success: bool = True):
    """Log agent interaction with structured data."""
    logfire = get_logfire()
    if not logfire:
        return
    
    try:
        logfire.info(
            "Agent interaction completed",
            agent_id=agent_id,
            user_id=user_id,
            project_id=project_id or "none",
            message_length=len(message),
            response_time_ms=response_time_ms,
            success=success,
            tags=["agent", "chat", agent_id]
        )
    except Exception as e:
        logger.debug(f"Error logging agent interaction: {e}")

def log_vector_search(project_id: str, query: str, result_count: int, 
                     execution_time: float, success: bool = True):
    """Log vector search operations with performance metrics."""
    logfire = get_logfire()
    if not logfire:
        return
    
    try:
        logfire.info(
            "Vector search completed",
            project_id=project_id,
            query_length=len(query),
            result_count=result_count,
            execution_time_ms=int(execution_time * 1000),
            success=success,
            tags=["vector", "search", "milvus"]
        )
    except Exception as e:
        logger.debug(f"Error logging vector search: {e}")

def log_document_processing(document_name: str, operation: str, 
                          processing_time: float, success: bool = True):
    """Log document processing operations."""
    logfire = get_logfire()
    if not logfire:
        return
    
    try:
        logfire.info(
            f"Document {operation} completed",
            document_name=document_name,
            operation=operation,
            processing_time_ms=int(processing_time * 1000),
            success=success,
            tags=["document", operation, "processing"]
        )
    except Exception as e:
        logger.debug(f"Error logging document processing: {e}")

def log_error(error: Exception, context: str, **kwargs):
    """Log errors with context and structured data."""
    logfire = get_logfire()
    if not logfire:
        return
    
    try:
        logfire.error(
            f"Error in {context}",
            error_type=type(error).__name__,
            error_message=str(error),
            context=context,
            tags=["error", context],
            **kwargs
        )
    except Exception as e:
        logger.debug(f"Error logging error: {e}")

def shutdown_logfire():
    """Clean shutdown of Logfire."""
    global _logfire_instance
    if _logfire_instance:
        try:
            # Flush any pending logs
            logger.info("🔧 Shutting down Logfire")
            _logfire_instance = None
        except Exception as e:
            logger.warning(f"⚠️ Error during Logfire shutdown: {e}")
