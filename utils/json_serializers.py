"""
JSON serialization utilities for handling datetime and Pydantic objects.
Fixes the "Object of type datetime is not JSON serializable" error.
"""

import json
from datetime import datetime, date
from decimal import Decimal
from typing import Any, Dict
from uuid import UUID

try:
    from pydantic import BaseModel
    PYDANTIC_AVAILABLE = True
except ImportError:
    PYDANTIC_AVAILABLE = False


class ThoughtChainJSONEncoder(json.JSONEncoder):
    """Custom JSON encoder for thought chain data that handles datetime, UUID, and Pydantic objects."""
    
    def default(self, obj: Any) -> Any:
        """Convert non-JSON serializable objects to JSON serializable format."""
        
        # Handle datetime objects
        if isinstance(obj, datetime):
            return obj.isoformat()
        
        # Handle date objects
        if isinstance(obj, date):
            return obj.isoformat()
        
        # Handle UUID objects
        if isinstance(obj, UUID):
            return str(obj)
        
        # Handle Decimal objects
        if isinstance(obj, Decimal):
            return float(obj)
        
        # Handle Pydantic models
        if PYDANTIC_AVAILABLE and isinstance(obj, BaseModel):
            # Try Pydantic v2 method first
            if hasattr(obj, 'model_dump'):
                try:
                    return obj.model_dump(mode='json')
                except Exception:
                    pass
            
            # Fallback to Pydantic v1 method
            if hasattr(obj, 'dict'):
                try:
                    return obj.dict()
                except Exception:
                    pass
        
        # Let the base class raise the TypeError
        return super().default(obj)


def safe_json_dumps(data: Any, **kwargs) -> str:
    """
    Safely serialize data to JSON string with proper datetime handling.
    
    Args:
        data: The data to serialize
        **kwargs: Additional arguments passed to json.dumps
    
    Returns:
        JSON string representation of the data
    """
    return json.dumps(data, cls=ThoughtChainJSONEncoder, **kwargs)


def serialize_thought_chain_data(obj: Any) -> Dict[str, Any]:
    """
    Smart serialization for thought chain data that handles Pydantic models properly.
    
    Args:
        obj: The object to serialize (typically a Pydantic model)
        
    Returns:
        Dictionary representation suitable for JSON serialization
    """
    if not PYDANTIC_AVAILABLE:
        return obj
    
    if isinstance(obj, BaseModel):
        # Try Pydantic v2 method first (preferred)
        if hasattr(obj, 'model_dump'):
            try:
                return obj.model_dump(mode='json')
            except Exception:
                pass
        
        # Fallback to Pydantic v1 method
        if hasattr(obj, 'dict'):
            try:
                return obj.dict()
            except Exception:
                pass
        
        # Last resort: use custom serialization
        result = {}
        for field_name, field_value in obj.__dict__.items():
            if isinstance(field_value, datetime):
                result[field_name] = field_value.isoformat()
            elif isinstance(field_value, date):
                result[field_name] = field_value.isoformat()
            elif isinstance(field_value, UUID):
                result[field_name] = str(field_value)
            elif isinstance(field_value, Decimal):
                result[field_name] = float(field_value)
            elif isinstance(field_value, BaseModel):
                result[field_name] = serialize_thought_chain_data(field_value)
            else:
                result[field_name] = field_value
        return result
    
    return obj


def create_thought_update_json(update_type: str, data: Any) -> str:
    """
    Create a properly formatted JSON string for thought chain updates.
    
    Args:
        update_type: The type of update ("thought_update", "thought_chain_complete", etc.)
        data: The data to include in the update
        
    Returns:
        Formatted JSON string ready for streaming
    """
    try:
        # Serialize the data properly
        serialized_data = serialize_thought_chain_data(data)
        
        # Create the update structure
        update_structure = {
            "type": update_type,
            "data": serialized_data
        }
        
        # Return JSON string
        return safe_json_dumps(update_structure)
        
    except Exception as e:
        # Fallback: create a simple error structure
        fallback_structure = {
            "type": "thought_chain_error",
            "data": {
                "error": f"Serialization error: {str(e)}",
                "original_type": update_type
            }
        }
        return json.dumps(fallback_structure)