#!/usr/bin/env python3

# Simple test script for the site diary content parser
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tools.site_diary.content_parser import _parse_site_diary_content_impl
import json

# Test with sample content from the current_output.md
sample_content = '''TTARIKH : 26/02/2025 (RABU) JABATAN KERJA RAYA MALAYSIA CUACA: ELOK (Nyatakan CUACA ELOK atau HUJAN) WAKTU MULA HUJAN: WAKTU TAMAT HUJAN: CATATAN: EARTH SOGGY DUE TO RAIN LAST NIGHT 1 2 | KERJA YANG DIBINA HARI INI: Status Kemajuan Sedang laksana [sap] Lokas! Aktiviti/Kerja <PERSON> | Waktu Tamat Aktiviti/Kerja [BILANGAN PEKERJA DI TAPAK BINA Jenis Kerja Kontraktor 1. Site Safety Monitoring 2. Install cones at working area & control live traffic Sub Kontraktor Dinamakan (NSC) Nota: <PERSON>kod dan <PERSON>… | - | Aktiviti/Kerja | Status Kemajuan | Lokasi Aktiviti/Kerja} | Waktu Mula | | Waktu Tamat | | — | — | — | — | — | — | | Sedang laksana | | BILANGAN PEKERJA DI TAPAK BINA. | | - | - | - | wi ores | - | - | | Bil. | Jenis Kerja | - | - | - | | - | Bumiputera | - | - | | Kontraktor | - | - | - | - | - | | 1. Site Safety Monitoring 2. Jnstall cones at w… | - | - | - | | 2 | - | 2 | | - | - | - | Jumlah | - | - | | Sub Kontraktor Dinamakan (NSC)'''

print("Testing site diary content parser...")
result = _parse_site_diary_content_impl(sample_content, 'Site Diary - 250226-Y-8al.pdf')
print('Parsing Result:')
print(json.dumps(result, indent=2))
